- @azure Rule - Use Azure Best Practices: When generating code for Azure, running terminal commands for Azure, or performing operations related to Azure, invoke your `azure_development-get_best_practices` tool if available.
- @azure Rule - Always list subscriptions and switch to each of them when required to performing operations that invoves resource in different subscriptions.
- @terraform Rule - Use Terraform Best Practices: When generating code for Terraform, running terminal commands for Terraform, or performing operations related to Terraform, invoke your `terraform_development-get_best_practices` tool if available.
- @terraform Rule - Always keep your code Logical and Modular: When generating Terraform code, ensure that the code is organized into logical modules and follows best practices for modularity.