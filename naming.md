Azure Resource Naming Convention

Keep this document up to date with the latest version of the naming convention: https://gbagroup.atlassian.net/wiki/x/AYB4

| Version | Date       | Author     | Reviewer   | Status | Description                       |
| ------- | ---------- | ---------- | ---------- | ------ | --------------------------------- |
| 0.1     | 2024       | @David Sun | @David Sun | Done   | Initial draft                     |
| 1.0     | 2025-01-06 | @David Sun | @Bing Luo  | Review | Upgrade according to <PERSON>'s draft |
|         |            |            |            |        |                                   |

Creating a standardized naming convention for Azure resources is crucial for maintaining organization, facilitating management and ensuring clarity across your infrastructure. Here's a comprehensive naming convention guideline that you can adapt according to your organization's specific requirements. Each resource name will typically be a combination of standardized abbreviations and identifiers to maintain consistency and scalability.

# General Guidelines

1. Length: Keep names concise. Azure has varying length restrictions depending on the resource type.

2. Scope Identification: Identify the scope or environment (e.g., Dev, Test, Prod).

3. Purpose: Ensure the function or purpose of the resource is evident.

4. Readability & Uniqueness: Ensure resource names are uniquely identifiable and use all lowercase characters to avoid case sensitivity issues and maintain consistency. Where allowed by Azure services, use delimiters like hyphens(-) or underscores(_) to enhance readability, noting that not all resource types support these characters.

We will provide examples of Azure resource naming conventions for different resource types to ensure this document is practical and implementable.

## Standard naming structure

```
<Environment>-<App/Service>-<ResourceType>-<Optional Components>
```

Note For shared resources, we can omit the environment or app/service name depending on the resource's purpose and scope. For example:

A shared container registry for storing organization-wide images could be named without the environment name.

A shared storage account for multiple applications could be named without the app/service name.

### Naming components

1. Environment: Short identifier for the environment. Examples:

Development: `dev`
QA: `qa`
Staging: `staging`
Production: `prod`
*More*

Note: Omit for global resources like Azure Active Directory or Traffic Manager etc.

2. Location: Azure region codes indicate where resources are deployed. While Microsoft doesn't provide official region codes, we'll use the widely-adopted codes from the community-maintained list at https://github.com/claranet/terraform-azurerm-regions/blob/master/REGIONS.md as our reference. These standardized codes help maintain consistency across our infrastructure.

UK South: `uks` (We can skip the region code in the resource name if the resource lives in UK South, since that is our default region)
UK West: `ukw`
*More*

3. Resource Type: Refer to official Microsoft documentation for Azure resource type abbreviations, list what we have used for now https://learn.microsoft.com/en-us/azure/cloud-adoption-framework/ready/azure-best-practices/resource-abbreviations

Resource group: `rg`
Virtual Network: `vnet`
Virtual network subnet: `snet`
Virtual machine: `vm`
Network interface (NIC): `nic`
Network security group (NSG): `nsg`
Virtual network peering: `peer`
App Service plan: `asp`
Web app: `app`
Azure SQL Database server: `sql`
Azure SQL Elastic Pool: `sqlep`
Storage account: `st`
Azure Cache for Redis instance: `redis`
Public IP address: `pip`
Key vault: `kv`
Route table: `rt`
Power BI Embedded: `pbi`
*More*

4. Application/Service/Role: Abbreviation of the application or service name to which the resource belongs. This could be a project code or function name.

## Naming Convention Variations with Examples

This section provides examples that cover the variations of the naming convention <Environment>-<App/Service>-<ResourceType>-<Optional Components>. Resources are grouped based on common naming patterns and specific use cases.

# Examples for <Environment>-<App/Service>-<ResourceType> (Back to Top)

These examples apply to resources that include only the environment, app/service, and resource type without additional components.

| Resource Type          | Naming Convention           | Example          | Purpose                                             |
| ---------------------- | --------------------------- | ---------------- | --------------------------------------------------- |
| Resource Group         | <Environment>-<App>-rg      | prod-salesapp-rg | Resource group for Sales Application in Production. |
| Virtual Network        | <Environment>-<App>-vnet    | dev-hrms-vnet    | Virtual network for HRMS in Development.            |
| Network Security Group | <Environment>-<App>-nsg     | qa-salesapp-nsg  | NSG for SalesApp in QA environment.                 |
| Subscription           | <Environment>-<Service>-sub | qa-gba-sub       | Subscription for QA of GB Agencies                  |

# Examples for <Environment>-<App/Service>-<ResourceType>-<Region> (Back to Top)

These examples apply to resources where region is included in the naming convention, typically for regional resources.

| Resource Type       | Naming Convention                | Example                | Purpose                                             |
| ------------------- | -------------------------------- | ---------------------- | --------------------------------------------------- |
| Application Gateway | <Environment>-<App>-agw-<region> | prod-webportal-agw-eus | Application Gateway for WebPortal in East US.       |
| Public IP           | <Environment>-<App>-pip-<region> | qa-webportal-pip-wus   | Public IP for WebPortal in West US.                 |
| DNS Zone            | <Environment>-<App>-dns-<region> | qa-marketing-dns-ne    | DNS Zone for Marketing application in North Europe. |

# Examples for <Environment>-<App/Service>-<ResourceType>-<Instance> (Back to Top)

These examples apply to resources where instance numbers are included, typically for resources with multiple instances.

| Resource Type   | Naming Convention                   | Example                | Purpose                                                             |
| --------------- | ----------------------------------- | ---------------------- | ------------------------------------------------------------------- |
| Virtual Machine | <Environment>-<App>-vm-<Instance>   | stg-marketing-vm-01    | First Virtual Machine for Marketing in Staging.                     |
| SQL Database    | <Environment>-<App>-sql-<Instance>  | prod-salesapp-sql-02   | Second SQL Database for SalesApp in Production.                     |
| Function App    | <Environment>-<App>-func-<Instance> | qa-analytics-func-01   | First Function App for Analytics in QA.                             |
| Virtual Machine | <Environment>-<App>-vm-<Instance>   | stg-marketing-vm-01a   | First Virtual Machine for Marketing in Staging, assigned to Zone A. |
| Virtual Machine | <Environment>-<App>-vm-<Instance>   | stg-marketing-vm-01b   | First Virtual Machine for Marketing in Staging, assigned to Zone B. |
| SQL Database    | <Environment>-<App>-sql-<Instance>  | prod-analytics-sql-02a | Second SQL Database for Analytics in Production, located in Zone A. |
| SQL Database    | <Environment>-<App>-sql-<Instance>  | prod-analytics-sql-02b | Second SQL Database for Analytics in Production, located in Zone B. |

Examples for <Environment>-<App/Service>-<ResourceType>-<Region>-<Instance> (Back to Top)

These examples apply to resources where both region and instance number are included, typically for global or regional scaling.

| Resource Type           | Naming Convention                           | Example                   | Purpose                                                         |
| ----------------------- | ------------------------------------------- | ------------------------- | --------------------------------------------------------------- |
| Virtual Machine         | <Environment>-<App>-vm-<Region>-<Instance>  | stg-salesapp-vm-eus-01    | First Virtual Machine for SalesApp in East US.                  |
| Application Gateway     | <Environment>-<App>-agw-<Region>-<Instance> | prod-webportal-agw-wus-02 | Second Application Gateway for WebPortal in West US.            |
| SQL Database            | <Environment>-<App>-sql-<Region>-<Instance> | qa-analytics-sql-ne-03    | Third SQL Database for Analytics in North Europe.               |
| Log Analytics Workspace | <Environment>-<App>-law-<Region>-<Instance> | prod-marketing-law-wus-01 | Log Analytics Workspace for Marketing in West US (Instance 01). |
| Public IP               | <Environment>-<App>-pip-<Region>-<Instance> | prod-salesapp-pip-eus-01r | First Public IP for SalesApp in East US.                        |

## Examples for <Environment> Omitted (Back to Top)

These examples apply to global resources where environment is not required.

| Resource Type            | Naming Convention | Example       | Purpose                                    |
| ------------------------ | ----------------- | ------------- | ------------------------------------------ |
| Azure Active Directory   | <App>-aad         | salesapp-aad  | Azure AD instance for SalesApp.            |
| Traffic Manager          | <App>-tm          | marketing-tm  | Traffic Manager for Marketing application. |
| Content Delivery Network | <App>-cdn         | webportal-cdn | CDN endpoint for WebPortal.                |

## Examples for <App/Service> Omitted (Back to Top)

These examples apply to organizational or infrastructure-level resources where app/service is not required.

Resource Type Naming Convention Example Purpose

| Management Group | <Environment>-mg | prod-mg | Management Group for Production. |
| ---------------- | ---------------- | ------- | -------------------------------- |

## Examples for Resources Without Hyphens (Back to Top)

These examples apply to resources that cannot include hyphens, such as Storage Accounts and Cosmos DB.

| Resource Type   | Naming Convention                          | Example                 | Purpose                                  |
| --------------- | ------------------------------------------ | ----------------------- | ---------------------------------------- |
| Storage Account | <Environment><App>st<Region><Instance>     | prodhrmsstuks01         | Storage Account for HRMS in UK South.    |
| Cosmos DB       | <Environment><App>cosmos<Region><Instance> | qaanalyticscosmosn eu01 | Cosmos DB for Analytics in North Europe. |

## Exceptions

Global Uniqueness: Some Azure resources require globally unique names (e.g., storage accounts, public IP addresses).

Policy Compliances: Ensure naming conventions comply with any regulatory or policy requirements specific to your industry or organization.

Automated Naming: Consider using automated scripts or tools for resource deployment that can enforce naming conventions.

Adjust the naming conventions according to any specific constraints or requirements your organization may have, such as prefix or suffix standards, and make sure they are well-documented and communicated to all relevant stakeholders involved in managing Azure resources.
