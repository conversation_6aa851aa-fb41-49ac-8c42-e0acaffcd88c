resource "azurerm_mssql_server" "sql_server" {
  name                         = "${var.environment}-sql-gbagroup"
  resource_group_name          = azurerm_resource_group.app.name
  location                     = azurerm_resource_group.app.location
  version                      = "12.0"
  administrator_login          = "sqladmin"
  administrator_login_password = random_password.sql_admin_password.result

  public_network_access_enabled = true # For dev only
  minimum_tls_version           = "1.2"
  connection_policy             = "Default"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      administrator_login_password,
      tags
    ]
  }
}

resource "random_password" "sql_admin_password" {
  length  = 16
  special = true
}

# Allow Azure Services
resource "azurerm_mssql_firewall_rule" "allow_azure_services" {
  name             = "AllowAzureServices"
  server_id        = azurerm_mssql_server.sql_server.id
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}

# resource "azurerm_mssql_elasticpool" "elastic_pool" {
#   name                = "${var.environment}-sqlpool-gbagroup"
#   resource_group_name = azurerm_resource_group.app.name
#   location            = azurerm_resource_group.app.location
#   server_name         = azurerm_mssql_server.sql_server.name
#   max_size_gb         = 50
#   license_type        = "LicenseIncluded"

#   sku {
#     name     = "StandardPool"
#     tier     = "Standard"
#     capacity = 50
#   }

#   per_database_settings {
#     min_capacity = 0
#     max_capacity = 50
#   }

#   tags = local.common_tags

#   lifecycle {
#     ignore_changes = [
#       tags
#     ]
#   }
# }
