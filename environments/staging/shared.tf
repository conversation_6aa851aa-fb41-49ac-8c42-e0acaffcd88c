resource "azurerm_resource_group" "shared" {
  name     = "${var.environment}-rg-shared"
  location = var.location

  tags = {
    Environment = var.environment
    ManagedBy   = "terraform"
  }
}

resource "azurerm_storage_account" "shared" {
  name                     = "${var.environment}stshared"
  resource_group_name      = azurerm_resource_group.shared.name
  location                 = azurerm_resource_group.shared.location
  account_kind             = "StorageV2"
  account_tier             = "Standard"
  account_replication_type = "LRS"
  min_tls_version          = "TLS1_2"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags["CreatedAt"],
    ]
  }
}
