module "oneview_backend_app" {
  source = "../../modules/appservice/linux"

  name                       = "oneview-backend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  custom_domains             = ["staging.api.oneview.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"

  container_registry_id = var.container_registry_id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"                          = "Staging"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE"             = "false"
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0"
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=160cf9ba-3a73-47a0-9895-7384908a7243;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=7edbb8d1-419f-4b6c-b26f-ffa2e3cb9b4c" # sensitive value
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }
  use_key_vault                     = true
  key_vault_name                    = "kv-oneview-be"
  health_check_path                 = "/api/Status"
  health_check_eviction_time_in_min = 5

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}

module "oneview_frontend_app" {
  source = "../../modules/appservice/linux"

  name                       = "oneview-frontend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  client_affinity_enabled    = false
  custom_domains             = ["staging.oneview.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"
  container_registry_id      = var.container_registry_id
  environment_variables = {
    "WEBSITES_PORT"                       = "3000"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
  }
  use_key_vault = false

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}

module "tms_backend_app" {
  source = "../../modules/appservice/linux"

  name                       = "tms-backend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  custom_domains             = ["staging.api.tms.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"
  container_registry_id      = var.container_registry_id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"                          = "Staging",
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0"
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=e5190aba-234b-4db3-aa06-bc7c79ed29a3;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=371a650b-7c00-4fa3-b246-96e15fc8cf45"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE"             = "false"
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }
  use_key_vault  = true
  key_vault_name = "kv-tms-be"

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}
