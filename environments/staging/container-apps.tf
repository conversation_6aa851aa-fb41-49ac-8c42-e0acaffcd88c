module "container_environment" {
  source = "../../modules/container-app-environment"

  name                       = "staging-capp"
  resource_group_name        = azurerm_resource_group.app.name
  location                   = azurerm_resource_group.app.location
  infrastructure_subnet_id   = module.networking_app.subnet_ids["staging-containerapps-snet"]
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  container_registry_id      = var.container_registry_id
  enable_private_endpoint    = false # Keeping simple for staging

  depends_on = [
    module.networking_app
  ]

  container_apps = [
    {
      name          = "debezium-connect"
      revision_mode = "Single"
      ingress = {
        allow_insecure_connections = false
        external_enabled           = true
        target_port                = 8083
        transport                  = "http"
        traffic_weight = [
          {
            percentage      = 100
            latest_revision = true
          }
        ]
      }
      template = {
        containers = [
          {
            name   = "debezium-connect" #CDC
            image  = "quay.io/debezium/connect:2.7"
            cpu    = 1.0
            memory = "2Gi"
            env = [
              {
                name  = "BOOTSTRAP_SERVERS"
                value = "${azurerm_eventhub_namespace.cdc.name}.servicebus.windows.net:9093"
              },
              {
                name  = "GROUP_ID"
                value = "1"
              },
              {
                name  = "CONFIG_STORAGE_TOPIC"
                value = "debezium_configs"
              },
              {
                name  = "OFFSET_STORAGE_TOPIC"
                value = "debezium_offsets"
              },
              {
                name  = "STATUS_STORAGE_TOPIC"
                value = "debezium_statuses"
              },
              {
                name  = "CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE"
                value = "false"
              },
              {
                name  = "CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE"
                value = "true"
              },
              {
                name  = "CONNECT_REQUEST_TIMEOUT_MS"
                value = "60000"
              },
              {
                name  = "CONNECT_SECURITY_PROTOCOL"
                value = "SASL_SSL"
              },
              {
                name  = "CONNECT_SASL_MECHANISM"
                value = "PLAIN"
              },
              {
                name  = "CONNECT_SASL_JAAS_CONFIG"
                value = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";"
              },
              {
                name  = "CONNECT_PRODUCER_SECURITY_PROTOCOL"
                value = "SASL_SSL"
              },
              {
                name  = "CONNECT_PRODUCER_SASL_MECHANISM"
                value = "PLAIN"
              },
              {
                name  = "CONNECT_PRODUCER_SASL_JAAS_CONFIG"
                value = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";"
              },
              {
                name  = "CONNECT_CONSUMER_SECURITY_PROTOCOL"
                value = "SASL_SSL"
              },
              {
                name  = "CONNECT_CONSUMER_SASL_MECHANISM"
                value = "PLAIN"
              },
              {
                name  = "CONNECT_CONSUMER_SASL_JAAS_CONFIG"
                value = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";"
              },
              {
                name  = "SQL_SERVER_CONNECTION_STRING"
                value = "jdbc:sqlserver://${azurerm_mssql_server.sql_server.name}.database.windows.net:1433;database=master;user=${azurerm_mssql_server.sql_server.administrator_login};password=${random_password.sql_admin_password.result};encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;"
              }
            ]
          },
          {
            name    = "debezium-connector-setup"
            image   = "curlimages/curl:latest"
            cpu     = 0.25
            memory  = "0.5Gi"
            command = ["/bin/sh"]
            args = [
              "-c",
              <<-EOT
              echo "Waiting for Debezium Connect to be ready..."
              until curl -f http://debezium-connect:8083/connectors; do
                echo "Debezium Connect not ready, waiting 10 seconds..."
                sleep 10
              done
              echo "Debezium Connect is ready, creating connector..."
              curl -X POST \
                -H "Content-Type: application/json" \
                -d '{
                  "name": "${local.cdc_name}",
                  "config": {
                    "snapshot.mode": "no_data",
                    "connector.class": "io.debezium.connector.sqlserver.SqlServerConnector",
                    "database.hostname": "${azurerm_mssql_server.sql_server.name}.database.windows.net",
                    "database.port": "1433",
                    "database.user": "${azurerm_mssql_server.sql_server.administrator_login}",
                    "database.password": "${random_password.sql_admin_password.result}",
                    "database.names": "OneviewStaging",
                    "driver.encrypt": "false",
                    "driver.trustServerCertificate": "true",
                    "schema.history.internal.kafka.bootstrap.servers": "${azurerm_eventhub_namespace.cdc.name}.servicebus.windows.net:9093",
                    "schema.history.internal.kafka.topic": "schema-history",
                    "schema.history.internal.consumer.security.protocol": "SASL_SSL",
                    "schema.history.internal.consumer.sasl.mechanism": "PLAIN",
                    "schema.history.internal.consumer.sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";",
                    "schema.history.internal.producer.security.protocol": "SASL_SSL",
                    "schema.history.internal.producer.sasl.mechanism": "PLAIN",
                    "schema.history.internal.producer.sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";",
                    "table.include.list": "dbo.Stock",
                    "tombstones.on.delete": "false",
                    "topic.prefix": "oneview-staging",
                    "transforms": "Reroute",
                    "transforms.Reroute.type": "io.debezium.transforms.ByLogicalTableRouter",
                    "transforms.Reroute.topic.regex": "oneview-staging\\\\.(.*)",
                    "transforms.Reroute.topic.replacement": "${local.cdc_name}",
                  }
                }' \
                http://debezium-connect:8083/connectors/
              EOT
            ]
          }
        ]
        min_replicas = 1
        max_replicas = 3
      }
    }
  ]

  tags = local.common_tags
}
