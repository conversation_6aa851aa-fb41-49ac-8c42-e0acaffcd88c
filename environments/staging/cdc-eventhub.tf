resource "azurerm_eventhub_namespace" "cdc" {
  name                = "staging-debezium-ehns"
  location            = "UK South"
  resource_group_name = azurerm_resource_group.app.name
  sku                 = "Standard"
  capacity            = 1

  tags = {
    Accessibility       = "Internal,On-premise,External,Public"
    Application         = "Not-Set"
    BusinessCriticality = "Low/Medium/High/Critical"
    CostCenter          = "Not-Set"
    CreatedBy           = "Unknown"
    CreationDate        = "2025-06-25"
    Environment         = "stg"
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "Not-Set"
    Usage               = "DEV2025/CanBeRemoved/NeedsReview"
  }
}

resource "azurerm_eventhub" "schema_history" {
  name                = "schema-history"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
  partition_count     = 1
  message_retention   = 7
}

# Comment out below resource as it will be created by debezium-connector
# resource "azurerm_eventhub" "changes" {
#   name                = local.cdc_name
#   namespace_name      = azurerm_eventhub_namespace.cdc.name
#   resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
#   partition_count     = 2
#   message_retention   = 7
# }

resource "azurerm_eventhub" "changes_dlq" {
  name                = "${local.cdc_name}-dlq"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
  partition_count     = 1
  message_retention   = 7
}

resource "azurerm_eventhub_consumer_group" "changes_cg" {
  name                = "${local.cdc_name}-cg"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  eventhub_name       = local.cdc_name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
}

resource "azurerm_eventhub_consumer_group" "changes_dlq_cg" {
  name                = "${local.cdc_name}-dlq-cg"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  eventhub_name       = azurerm_eventhub.changes_dlq.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
}

resource "azurerm_eventhub_namespace_authorization_rule" "cdc_root" {
  name                = "TheRootManageSharedAccessKey"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name

  listen = true
  send   = true
  manage = true
}