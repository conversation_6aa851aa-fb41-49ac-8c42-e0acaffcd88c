terraform {
  backend "azurerm" {
    resource_group_name  = "infra-management"
    storage_account_name = "gbagrouptfstate"
    container_name       = "tfstate"
    key                  = "mibrid.tfstate"
  }

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.11.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}
