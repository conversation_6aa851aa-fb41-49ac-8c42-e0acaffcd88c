resource "random_password" "sql_admin_password" {
  length  = 16
  special = true
}

resource "azurerm_mssql_server" "sql_server" {
  name                         = "${var.environment}gbagroup"
  resource_group_name          = azurerm_resource_group.app.name
  location                     = azurerm_resource_group.app.location
  version                      = "12.0"
  administrator_login          = "sqladmin"
  administrator_login_password = random_password.sql_admin_password.result

  public_network_access_enabled = true # For dev only
  minimum_tls_version           = "1.2"
  connection_policy             = "Default"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      administrator_login_password,
      tags
    ]
  }
}

# resource "azurerm_mssql_elasticpool" "elastic_pool" {
#   name                = "${var.environment}-elasticpool"
#   resource_group_name = azurerm_resource_group.app.name
#   location            = azurerm_resource_group.app.location
#   server_name         = azurerm_mssql_server.sql_server.name
#   max_size_gb         = 400
#   license_type        = "LicenseIncluded"

#   sku {
#     name     = "StandardPool"
#     tier     = "Standard"
#     capacity = 400
#   }

#   per_database_settings {
#     min_capacity = 0
#     max_capacity = 100
#   }

#   tags = local.common_tags

#   lifecycle {
#     ignore_changes = [
#       tags
#     ]
#   }
# }
