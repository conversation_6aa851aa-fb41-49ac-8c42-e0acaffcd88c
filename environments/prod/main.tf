provider "azurerm" {
  features {}
}

locals {
  common_tags = {
    CreatedBy   = "Terraform"
    CreatedAt   = timestamp()
    OwnedBy     = "Cameron<PERSON><EMAIL>"
    Environment = var.environment
  }
}

data "azurerm_client_config" "current" {}

resource "azurerm_resource_group" "app" {
  name     = format("%s-rg-app", var.environment)
  location = var.location
  tags     = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_log_analytics_workspace" "log_analytics_workspace" {
  name                = format("%s-log-analytics-workspace", var.environment)
  location            = azurerm_resource_group.app.location
  resource_group_name = azurerm_resource_group.app.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
  tags                = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_service_plan" "app_linux_plan" {
  name                = "${var.environment}-asp-linux-plan"
  resource_group_name = azurerm_resource_group.app.name
  location            = azurerm_resource_group.app.location
  # os_type can be `Windows`, `Linux`, and `WindowsContainer`
  os_type = "Linux"
  # SKU names https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/service_plan#sku_name-1
  # Pricing tier https://azure.microsoft.com/en-us/pricing/details/app-service/linux/
  sku_name = "P1v3"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

module "networking_app" {
  source = "../../modules/networking"

  address_space       = ["**********/16"]
  environment         = var.environment
  location_short      = var.location_short
  resource_group_name = azurerm_resource_group.app.name
  subnets = [
    {
      address_prefix = "**********/24"
      name           = format("%s-%s-snet-backend", var.environment, var.location_short)
    }
  ]

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}
