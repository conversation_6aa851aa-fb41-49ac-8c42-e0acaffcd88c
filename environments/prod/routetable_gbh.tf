# GBH Route Rules

# VLAN 101 - Data
resource "azurerm_route" "gbh_1" {
  name                   = "GBH-10-3-1-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 102 - Voice
resource "azurerm_route" "gbh_2" {
  name                   = "GBH-10-3-2-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 103 - CCTV
resource "azurerm_route" "gbh_3" {
  name                   = "GBH-10-3-3-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 104 - Access Control
resource "azurerm_route" "gbh_4" {
  name                   = "GBH-10-3-4-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 105 - Audio Visual
resource "azurerm_route" "gbh_5" {
  name                   = "GBH-10-3-5-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 106 - WiFi 01
resource "azurerm_route" "gbh_6" {
  name                   = "GBH-10-3-6-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 107 - WiFi 02
resource "azurerm_route" "gbh_7" {
  name                   = "GBH-10-3-7-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 108 - MGMT
resource "azurerm_route" "gbh_8" {
  name                   = "GBH-10-3-8-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 109 - Spare 01 (Reserved)
resource "azurerm_route" "gbh_9" {
  name                   = "GBH-10-3-9-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 110 - Spare 02 (Reserved)
resource "azurerm_route" "gbh_10" {
  name                   = "GBH-10-3-10-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
