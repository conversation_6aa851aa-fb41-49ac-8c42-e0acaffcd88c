module "container_environment" {
  source = "../../modules/container-app-environment"

  name                       = "prod-capp"
  resource_group_name        = azurerm_resource_group.app.name
  location                   = azurerm_resource_group.app.location
  infrastructure_subnet_id   = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-containerapps-snet"
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  container_registry_id      = var.container_registry_id
  enable_private_endpoint    = true

  # Added to address: "endpoint_virtual_network_id must be provided"
  endpoint_virtual_network_id = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet"

  # Added to address: "container_app_environment_pe_name, private_endpoint_subnet_id_for_environment_pe, 
  # and private_dns_zone_id_for_environment_pe must be provided"
  container_app_environment_pe_name             = "prod-capp-env-pe" # Static name for the environment's private endpoint
  private_endpoint_subnet_id_for_environment_pe = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-containerapps-pe-snet"
  private_dns_zone_id_for_environment_pe        = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/privateDnsZones/privatelink.uksouth.azurecontainerapps.io"

  private_dns_zone_id        = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/privateDnsZones/privatelink.uksouth.azurecontainerapps.io"
  private_endpoint_subnet_id = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-containerapps-pe-snet"

  container_apps = [
    {
      name           = "job-service"
      revision_mode  = "Single"
      key_vault_name = "prod-jobservice-kv" # Added key_vault_name to the app
      ingress = {
        allow_insecure_connections = true
        external_enabled           = true
        target_port                = 80
        transport                  = "auto"
        traffic_weight = [
          {
            percentage      = 100
            latest_revision = true
          }
        ]
      }
      template = {
        containers = [
          {
            name   = "job-service"
            cpu    = 0.5
            memory = "1Gi"
            env = [
              {
                name  = "ASPNETCORE_ENVIRONMENT"
                value = "containerapp"
              }
            ]
          }
        ]
        min_replicas = 1
        max_replicas = 1 # Assuming max_replicas is 1 based on min_replicas
      }
      registry = {
        server = "gbagroup.azurecr.io"
        # identity will be automatically assigned by the module if not specified
      }
      tags = {
        Accessibility       = "Internal,On-premise"
        Application         = "Not-Set"
        BusinessCriticality = "High"
        BusinessUnit        = "Not-Set"
        CostCenter          = "Not-Set"
        CreatedAt           = "2025-05-27T15:43:36Z"
        CreatedBy           = "Terraform"
        DataSensitivity     = "General"
        CreationDate        = "2025-06-20"
        Department          = "Not-Set"
        Environment         = "prod"
        ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
        Owner               = "<EMAIL>"
        Project             = "Not-Set"
        Sites               = "Global"
        Usage               = "PROD2025/CanBeRemoved/NeedsReview"
      }
    },
    {
      name           = "dev-job-service"
      revision_mode  = "Single"
      key_vault_name = "dev-jobservice-kv"
      ingress = {
        allow_insecure_connections = true
        external_enabled           = true
        target_port                = 80
        transport                  = "auto"
        traffic_weight = [
          {
            percentage      = 100
            latest_revision = true
          }
        ]
      }
      template = {
        containers = [
          {
            name   = "job-service"
            cpu    = 0.5
            memory = "1Gi"
            env = [
              {
                name  = "ASPNETCORE_ENVIRONMENT"
                value = "containerapp-dev"
              }
            ]
          }
        ]
        min_replicas = 1
        max_replicas = 1
      }
      registry = {
        server = "gbagroup.azurecr.io"
      }
      tags = {
        Accessibility       = "Internal,On-premise"
        Application         = "Not-Set"
        BusinessCriticality = "High"
        BusinessUnit        = "Not-Set"
        CostCenter          = "Not-Set"
        CreatedAt           = "2025-05-27T15:43:36Z"
        CreatedBy           = "Terraform"
        CreationDate        = "2025-06-20"
        DataSensitivity     = "General"
        Department          = "Not-Set"
        Environment         = "prod"
        ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
        Owner               = "<EMAIL>"
        Project             = "Not-Set"
        Sites               = "Global"
        Usage               = "PROD2025/CanBeRemoved/NeedsReview"
      }
    }
  ]
  tags = {
    Accessibility       = "Internal,On-premise"
    Application         = "Container Apps"
    BusinessCriticality = "High"
    BusinessUnit        = "Not-Set"
    CostCenter          = "Not-Set"
    CreatedBy           = "Terraform"
    CreationDate        = "2025-06-20"
    DataSensitivity     = "General"
    Department          = "Not-Set"
    Environment         = "prod"
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "Not-Set"
    Sites               = "Global"
    Usage               = "PROD2025/CanBeRemoved/NeedsReview"
  }
}
