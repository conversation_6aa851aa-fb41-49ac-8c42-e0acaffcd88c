# Add role assignment for managing users
# Contributor role for <PERSON> on Mibrid
# resource "azurerm_role_assignment" "shirley_contributor" {
#   scope                = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839"
#   role_definition_name = "Contributor"
#   principal_id         = "358dc132-0692-46fd-a6b6-2a72e6757b8a"
# }

resource "azurerm_role_assignment" "function_app_tag_contributor" {
  scope                = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839"
  role_definition_name = "Tag Contributor"
  principal_id         = "e53805a3-518c-4375-ad04-3a7e5e2044ba"
}

resource "azurerm_role_assignment" "function_app_reader" {
  scope                = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839"
  role_definition_name = "Reader"
  principal_id         = "e53805a3-518c-4375-ad04-3a7e5e2044ba"
}
