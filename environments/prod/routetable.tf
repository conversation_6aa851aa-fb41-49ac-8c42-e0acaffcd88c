# Route Table Resource
resource "azurerm_route_table" "main" {
  name                = "prod-meraki-onprem-rt"
  location            = var.location
  resource_group_name = "prod-meraki-rg"

  # Disable BGP route propagation if needed
  bgp_route_propagation_enabled = true

  tags = merge(local.common_tags, {
    "Accessibility"       = "Internal,On-premise"
    "Application"         = "Network-Infrastructure"
    "BusinessCriticality" = "High"
    "BusinessUnit"        = "IT-Infrastructure"
    "CostCenter"          = "IT-Network-001"
    "CreatedAt"           = "2025-05-15T13:56:34Z"
    "CreatedBy"           = "Terraform"
    "CreationDate"        = "2025-05-15"
    "DataSensitivity"     = "Internal"
    "Department"          = "IT"
    "Environment"         = "prod"
    "ExpirationDate"      = "N/A"
    "OwnedBy"             = "<EMAIL>"
    "Owner"               = "<EMAIL>"
    "Project"             = "Network-Connectivity"
    "Sites"               = "Global"
    "Usage"               = "PROD2025"
  })
}

# Here add some routes for specific reasons

# For prod-foresight-be
resource "azurerm_route" "foresight" {
  name                   = "Foresight-10-11-7-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "************/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
