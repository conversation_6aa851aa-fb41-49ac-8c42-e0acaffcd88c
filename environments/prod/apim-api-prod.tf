# ----------------- APIM section | Prod API -----------------

resource "azurerm_api_management_api" "prod-api" {
  name                = "prod-api"
  resource_group_name = "prod-apigate-rg"
  api_management_name = "prod-gbagateways-apim"
  revision            = "1"
  display_name        = "Prod API"
  path                = "prod"
  protocols           = ["https"]
}

resource "azurerm_api_management_api_policy" "prod-policy" {
  api_name            = azurerm_api_management_api.prod-api.name
  api_management_name = azurerm_api_management_api.prod-api.api_management_name
  resource_group_name = azurerm_api_management_api.prod-api.resource_group_name

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <!-- Extract variables from the subscription context -->
        <set-variable name="Name" value="@(context.Subscription.Name)" />
        <!-- Set the Name to header "Client-Identity-Name" -->
        <set-header name="Client-Identity-Name" exists-action="override">
            <value>@(context.Variables.GetValueOrDefault<string>("Name"))</value>
        </set-header>
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}

# ----------------- APIM section | Arval Prod API | Operation: Check-in -----------------

resource "azurerm_api_management_api_operation" "prod-operation-checkin" {
  operation_id        = "prod-operation"
  api_name            = azurerm_api_management_api.prod-api.name
  api_management_name = "prod-gbagateways-apim"
  resource_group_name = "prod-apigate-rg"
  display_name        = "Check-in"
  method              = "POST"
  url_template        = "/new"
}

# # Depends on azurerm_api_management_api_operation
# # Depends on azurerm_api_management_backend
resource "azurerm_api_management_api_operation_policy" "prod-operation-checkin-policy" {
  api_name            = azurerm_api_management_api_operation.prod-operation-checkin.api_name
  api_management_name = azurerm_api_management_api_operation.prod-operation-checkin.api_management_name
  resource_group_name = azurerm_api_management_api_operation.prod-operation-checkin.resource_group_name
  operation_id        = azurerm_api_management_api_operation.prod-operation-checkin.operation_id

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <!-- Extract variables from the subscription context -->
        <set-variable name="Name" value="@(context.Subscription.Name)" />
        <set-variable name="AllowList" value="arval,bmw,Arval,stellantis" />
        <choose>
            <when condition="@(!((context.Variables.GetValueOrDefault&lt;string&gt;(&quot;AllowList&quot;) ?? &quot;&quot;).Split(',').Contains(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;Name&quot;))))">
                <return-response>
                    <set-status code="403" reason="Forbidden" />
                    <set-header name="Content-Type" exists-action="override">
                        <value>text/plain</value>
                    </set-header>
                    <set-body>Hey, do you have permission to access this API?</set-body>
                </return-response>
            </when>
        </choose>
        <send-request mode="copy" response-variable-name="authResult" timeout="20" ignore-error="false">
            <set-url>https://prod-apigate-func.azurewebsites.net/api/log-request</set-url>
            <set-method>POST</set-method>
            <set-header name="x-functions-key" exists-action="override">
                <value>${data.azurerm_function_app_host_keys.prod_apigate_func_keys.default_function_key}</value>
            </set-header>
            <set-body>@(context.Request.Body.As<string>(preserveContent: true))</set-body>
        </send-request>
        <set-backend-service id="apim-generated-policy" backend-id="${azurerm_api_management_backend.prod-apigate-func.name}" />
        <rewrite-uri template="/check-in" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}

# ----------------- APIM section | Arval Prod API | Operation: Headers -----------------

resource "azurerm_api_management_api_operation" "prod-operation-headers" {
  operation_id        = "prod-operation-headers"
  api_name            = azurerm_api_management_api.prod-api.name
  api_management_name = "prod-gbagateways-apim"
  resource_group_name = "prod-apigate-rg"
  display_name        = "Headers"
  method              = "GET"
  url_template        = "/headers"
}

resource "azurerm_api_management_api_operation_policy" "prod-operation-headers-policy" {
  api_name            = azurerm_api_management_api_operation.prod-operation-headers.api_name
  api_management_name = azurerm_api_management_api_operation.prod-operation-headers.api_management_name
  resource_group_name = azurerm_api_management_api_operation.prod-operation-headers.resource_group_name
  operation_id        = azurerm_api_management_api_operation.prod-operation-headers.operation_id

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <!-- Extract variables from the subscription context -->
        <set-variable name="Name" value="@(context.Subscription.Name)" />
        <set-variable name="AllowList" value="arval,bmw,Arval" />
        <choose>
            <when condition="@(!((context.Variables.GetValueOrDefault&lt;string&gt;(&quot;AllowList&quot;) ?? &quot;&quot;).Split(',').Contains(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;Name&quot;))))">
                <return-response>
                    <set-status code="403" reason="Forbidden" />
                    <set-header name="Content-Type" exists-action="override">
                        <value>text/plain</value>
                    </set-header>
                    <set-body>Hey, do you have permission to access this API?</set-body>
                </return-response>
            </when>
        </choose>
        <send-request mode="copy" response-variable-name="authResult" timeout="20" ignore-error="false">
            <set-url>https://prod-apigate-func.azurewebsites.net/api/headers</set-url>
            <set-method>GET</set-method>
            <set-header name="x-functions-key" exists-action="override">
                <value>${data.azurerm_function_app_host_keys.prod_apigate_func_keys.default_function_key}</value>
            </set-header>
        </send-request>
        <set-backend-service id="apim-generated-policy" backend-id="${azurerm_api_management_backend.prod-apigate-func.name}" />
        <rewrite-uri template="/headers" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}
