# resource group for it already exists

locals {
  bmwreports_app_name         = "bmwreports"
  bmwreports_app_service_name = "${var.environment}-${local.bmwreports_app_name}-app"
}

data "azurerm_resource_group" "bmwreports_app_rg" {
  name = "prod-bmw-rg"
}

# Add App Service
resource "azurerm_linux_web_app" "prod_bmwreports_app" {
  name                = local.bmwreports_app_service_name
  resource_group_name = data.azurerm_resource_group.bmwreports_app_rg.name
  location            = "uksouth"
  service_plan_id     = azurerm_service_plan.shared_linux_asp.id

  https_only                    = true
  client_affinity_enabled       = true
  public_network_access_enabled = true

  app_settings = {
    "APP_CONTEXT"                                     = "BMW_UVR"
    "TNR_UVR_NODE_ENV"                                = "prod"
    "APPINSIGHTS_INSTRUMENTATIONKEY"                  = "92f012dd-42c5-4545-a364-90b6e9752ae6"
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0"
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=92f012dd-42c5-4545-a364-90b6e9752ae6;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=83fec562-5941-46a2-96fd-c57f54d51633"
    "ApplicationInsightsAgent_EXTENSION_VERSION"      = "~3"
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }

  site_config {
    always_on              = true
    minimum_tls_version    = "1.2"
    http2_enabled          = true
    app_command_line       = "npm run start:prod"
    vnet_route_all_enabled = true
    ftps_state             = "FtpsOnly"

    application_stack {
      node_version = "20-lts"
    }
  }

  logs {
    detailed_error_messages = false
    failed_request_tracing  = false

    http_logs {
      file_system {
        retention_in_days = 30
        retention_in_mb   = 35
      }
    }
  }

  identity {
    type         = "SystemAssigned"
    identity_ids = null
  }

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags,
      sticky_settings,
      virtual_network_subnet_id
    ]
  }
}

# Add diagnostic settings for the App Service
data "azurerm_monitor_diagnostic_categories" "bmwreports_app_diagnostic_categories" {
  resource_id = azurerm_linux_web_app.prod_bmwreports_app.id
}

resource "azurerm_monitor_diagnostic_setting" "bmwreports_app_diagnostics" {
  name                       = "${local.bmwreports_app_name}-diagnostics"
  target_resource_id         = azurerm_linux_web_app.prod_bmwreports_app.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id

  dynamic "enabled_log" {
    for_each = data.azurerm_monitor_diagnostic_categories.bmwreports_app_diagnostic_categories.log_category_types
    content {
      category = enabled_log.value
    }
  }

  metric {
    category = "AllMetrics"
    enabled  = true
  }
}

# Add access policy for the App Service
resource "azurerm_key_vault_access_policy" "bmwreports_app_key_vault_access_policy" {
  key_vault_id = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-bmw-rg/providers/Microsoft.KeyVault/vaults/prod-bmw-kv"
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.prod_bmwreports_app.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}

resource "azurerm_app_service_virtual_network_swift_connection" "bmwreports_app_vnet_swift_connection" {
  app_service_id = azurerm_linux_web_app.prod_bmwreports_app.id
  subnet_id      = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
}

# Add custom domain for the App Service
module "bmwreports_app_custom_domain" {
  source = "../../modules/appservice/custom-domain"

  domain_name                   = "bmwreports.gbatechnologies.net"
  app_service_name              = local.bmwreports_app_service_name
  resource_group_name           = data.azurerm_resource_group.bmwreports_app_rg.name
  cloudflare_zone_id            = "f9919cce5fbe55edc69a584e174499dd"
  custom_domain_verification_id = "52202A17758C2FAA98A11175BED342C692A7F41499F667C54D82777B8ABA1B03"
  app_service_default_hostname  = azurerm_linux_web_app.prod_bmwreports_app.default_hostname

  tags = {
    Accessibility       = "Internal,On-premise,External,Public"
    Application         = "Not-Set"
    BusinessCriticality = "Low/Medium/High/Critical"
    BusinessUnit        = "Not-Set"
    CostCenter          = "Not-Set"
    CreatedBy           = "Unknown"
    DataSensitivity     = "General"
    Department          = "Not-Set"
    Environment         = "prod"
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "Not-Set"
    Sites               = "Global"
    Usage               = "PROD2025/CanBeRemoved/NeedsReview"
  }
}
