locals {
  ineos_app_name = "gbagrenadier"
}

resource "azurerm_resource_group" "ineos_app" {
  name     = "prod-${local.ineos_app_name}-rg"
  location = var.location

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

# Add service plan
resource "azurerm_service_plan" "ineos_app_linux_plan" {
  name                = "${var.environment}-${local.ineos_app_name}-asp-linux-plan"
  resource_group_name = azurerm_resource_group.ineos_app.name
  location            = azurerm_resource_group.ineos_app.location
  # os_type can be `Windows`, `Linux`, and `WindowsContainer`
  os_type = "Linux"
  # SKU names https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/service_plan#sku_name-1
  # Pricing tier https://azure.microsoft.com/en-us/pricing/details/app-service/linux/
  sku_name     = "B2"
  worker_count = 1

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_key_vault" "ineos_app_vault" {
  name                        = "${var.environment}-${local.ineos_app_name}-kv"
  location                    = azurerm_resource_group.ineos_app.location
  resource_group_name         = azurerm_resource_group.ineos_app.name
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  sku_name                    = "standard"
  enabled_for_disk_encryption = true
  soft_delete_retention_days  = 7
  purge_protection_enabled    = true
  enable_rbac_authorization   = false

  network_acls {
    default_action = "Deny"
    virtual_network_subnet_ids = [
      "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
    ]
    bypass = "AzureServices"
  }

  tags = merge(local.common_tags, {
    KeyVaultFor = local.ineos_app_name
  })

  lifecycle {
    ignore_changes = [
      tags["CreatedAt"],
    ]
  }
}

data "azurerm_monitor_diagnostic_categories" "ineos_app_diagnostics" {
  resource_id = azurerm_linux_web_app.ineos_app.id
}

resource "azurerm_monitor_diagnostic_setting" "ineos_app_diagnostics" {
  name                       = "${local.ineos_app_name}-diagnostics"
  target_resource_id         = azurerm_linux_web_app.ineos_app.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id

  dynamic "enabled_log" {
    for_each = data.azurerm_monitor_diagnostic_categories.ineos_app_diagnostics.log_category_types
    content {
      category = enabled_log.value
    }
  }

  metric {
    category = "AllMetrics"
    enabled  = true
  }
}

# TODO: Check if the provider updates to support this
# resource "azurerm_monitor_diagnostic_setting" "ineos_app_vault_diagnostics" {
#   name                       = "${azurerm_key_vault.ineos_app_vault.name}-diagnostics"
#   target_resource_id         = azurerm_key_vault.ineos_app_vault.id
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id

#   dynamic "enabled_log" {
#     for_each = data.azurerm_monitor_diagnostic_categories.ineos_app_diagnostics.log_category_types
#     content {
#       category = enabled_log.value
#     }
#   }

#   metric {
#     category = "AllMetrics"
#     enabled  = true
#   }
# }

# Add access policy for the App <NAME_EMAIL>
resource "azurerm_key_vault_access_policy" "ineos_key_vault_access_policy" {
  key_vault_id = azurerm_key_vault.ineos_app_vault.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = "ea72f788-05c1-4c21-a3c4-cf994dae71ac"

  secret_permissions = [
    "Get",
    "List"
  ]
}

# Add App Service
resource "azurerm_linux_web_app" "ineos_app" {
  name                = "${var.environment}-${local.ineos_app_name}-app"
  resource_group_name = azurerm_resource_group.ineos_app.name
  location            = azurerm_resource_group.ineos_app.location
  service_plan_id     = azurerm_service_plan.ineos_app_linux_plan.id

  https_only                    = true
  client_affinity_enabled       = true
  public_network_access_enabled = true
  virtual_network_subnet_id     = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"

  site_config {
    always_on              = true
    minimum_tls_version    = "1.2"
    http2_enabled          = true
    vnet_route_all_enabled = true

    application_stack {
      dotnet_version = "6.0"
    }
  }

  logs {
    detailed_error_messages = false
    failed_request_tracing  = false

    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 35
      }
    }
  }

  identity {
    type         = "SystemAssigned"
    identity_ids = null
  }

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags,
      sticky_settings,
    ]
  }
}

# Add access policy for the App Service
resource "azurerm_key_vault_access_policy" "ineos_app_key_vault_access_policy" {
  key_vault_id = azurerm_key_vault.ineos_app_vault.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.ineos_app.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}

resource "random_password" "ineos_app_sql_admin_password" {
  length  = 16
  special = true
}

resource "azurerm_mssql_server" "ineos_app_sql_server" {
  name                         = "${var.environment}-${local.ineos_app_name}-sql"
  resource_group_name          = azurerm_resource_group.ineos_app.name
  location                     = azurerm_resource_group.ineos_app.location
  version                      = "12.0"
  administrator_login          = "sqladmin"
  administrator_login_password = random_password.ineos_app_sql_admin_password.result

  public_network_access_enabled = true # For dev only
  minimum_tls_version           = "1.2"
  connection_policy             = "Default"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      administrator_login_password,
      tags
    ]
  }
}

# resource "azurerm_mssql_elasticpool" "ineos_app_sql_elasticpool" {
#   name                = "${var.environment}-${local.ineos_app_name}-sqlep"
#   resource_group_name = azurerm_resource_group.ineos_app.name
#   location            = azurerm_resource_group.ineos_app.location
#   server_name         = azurerm_mssql_server.ineos_app_sql_server.name
#   max_size_gb         = 400
#   license_type        = "LicenseIncluded"

#   sku {
#     name     = "StandardPool"
#     tier     = "Standard"
#     capacity = 400
#   }

#   per_database_settings {
#     min_capacity = 0
#     max_capacity = 100
#   }

#   tags = local.common_tags

#   lifecycle {
#     ignore_changes = [
#       tags
#     ]
#   }
# }

resource "azurerm_mssql_firewall_rule" "ineos_app_sql_firewall_rule_allow_azure_services" {
  name             = "AllowAzureServices"
  server_id        = azurerm_mssql_server.ineos_app_sql_server.id
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}
