# Azure API Management (APIM) for Production

# The resource that is created in the Azure portal
# - APIM
# - App Service plan && Function App(Enable System Assigned Identity)
# The resource that is created by Terraform
# - API in APIM
# - API operation in APIM
# - API operation policy in APIM
# - Backend in APIM

# Data source to get the Function App principal ID
data "azurerm_linux_function_app" "prod_apigate_func" {
  name                = "prod-apigate-func"
  resource_group_name = "prod-apigate-rg"
}

data "azurerm_linux_function_app" "dev_apigate_func" {
  name                = "dev-apigate-func"
  resource_group_name = "prod-apigate-rg"
}

data "azurerm_function_app_host_keys" "prod_apigate_func_keys" {
  name                = "prod-apigate-func"
  resource_group_name = "prod-apigate-rg"
}

data "azurerm_function_app_host_keys" "dev_apigate_func_keys" {
  name                = "dev-apigate-func"
  resource_group_name = "prod-apigate-rg"
}

# ----------------- Key Vault section -----------------

# Key Vault for APIM/Function App secrets
resource "azurerm_key_vault" "apigate" {
  name                        = "prod-apigate-kv"
  location                    = "uksouth"
  resource_group_name         = "prod-apigate-rg"
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  sku_name                    = "standard"
  enabled_for_disk_encryption = true
  soft_delete_retention_days  = 7
  purge_protection_enabled    = true
  enable_rbac_authorization   = false

  network_acls {
    default_action = "Allow"
    bypass         = "AzureServices"
  }

  tags = {
    Accessibility       = "Internal"
    Application         = "api gateway"
    BusinessCriticality = "High"
    BusinessUnit        = "Not-Set"
    CostCenter          = "Not-Set"
    DataSensitivity     = "General"
    Department          = "Not-Set"
    ExpirationDate      = "Not-Set"
    Owner               = "<EMAIL>"
    Project             = "External Integration"
    Environment         = "prod"
    Sites               = "Not-Set"
    Usage               = "In Use"
  }
}

# Grant Function App access to Key Vault secrets
resource "azurerm_key_vault_access_policy" "func_app" {
  key_vault_id = azurerm_key_vault.apigate.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_linux_function_app.prod_apigate_func.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}

resource "azurerm_key_vault" "dev_apigate" {
  name                        = "dev-apigate-kv"
  location                    = "uksouth"
  resource_group_name         = "prod-apigate-rg"
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  sku_name                    = "standard"
  enabled_for_disk_encryption = true
  soft_delete_retention_days  = 7
  purge_protection_enabled    = true
  enable_rbac_authorization   = false

  network_acls {
    default_action = "Allow"
    bypass         = "AzureServices"
  }

  tags = {
    Accessibility       = "Internal"
    Application         = "api gateway"
    BusinessCriticality = "High"
    BusinessUnit        = "Not-Set"
    CostCenter          = "Not-Set"
    DataSensitivity     = "General"
    Department          = "Not-Set"
    ExpirationDate      = "Not-Set"
    Owner               = "<EMAIL>"
    Project             = "External Integration"
    Environment         = "prod"
    Sites               = "Not-Set"
    Usage               = "In Use"
  }
}

resource "azurerm_key_vault_access_policy" "dev_func_app" {
  key_vault_id = azurerm_key_vault.dev_apigate.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_linux_function_app.dev_apigate_func.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}

# Grant Terraform principal full access for management
resource "azurerm_key_vault_access_policy" "terraform_prod" {
  key_vault_id = azurerm_key_vault.apigate.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_client_config.current.object_id

  secret_permissions = [
    "Backup",
    "Delete",
    "Get",
    "List",
    "Recover",
    "Restore",
    "Set"
  ]
}

resource "azurerm_key_vault_access_policy" "terraform_dev" {
  key_vault_id = azurerm_key_vault.dev_apigate.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_client_config.current.object_id

  secret_permissions = [
    "Backup",
    "Delete",
    "Get",
    "List",
    "Recover",
    "Restore",
    "Set"
  ]
}

# ----------------- APIM section | Backend | prod-apigate-func -----------------

# Depends on APIM
resource "azurerm_api_management_backend" "prod-apigate-func" {
  name                = "prod-apigate-func"
  resource_group_name = "prod-apigate-rg"
  api_management_name = "prod-gbagateways-apim"
  protocol            = "http"
  url                 = "https://prod-apigate-func.azurewebsites.net/api"
  resource_id         = "https://management.azure.com/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourcegroups/prod-apigate-rg/providers/Microsoft.Web/sites/prod-apigate-func"

  credentials {
    certificate = []
    header = {
      "x-functions-key" = "{{apim-apigate-key}}"
    }
    query = {}
  }
}

resource "azurerm_api_management_backend" "dev-apigate-func" {
  name                = "dev-apigate-func"
  resource_group_name = "prod-apigate-rg"
  api_management_name = "prod-gbagateways-apim"
  protocol            = "http"
  url                 = "https://dev-apigate-func.azurewebsites.net/api"
  resource_id         = "https://management.azure.com/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourcegroups/prod-apigate-rg/providers/Microsoft.Web/sites/dev-apigate-func"

  credentials {
    certificate = []
    header = {
      "x-functions-key" = "{{apim-apigate-key-dev}}"
    }
    query = {}
  }
}

# ----------------- APIM section | Subscription -----------------

# Depends on azurerm_api_management_api

resource "azurerm_api_management_subscription" "arval-staging-subscription" {
  api_management_name = "prod-gbagateways-apim"
  display_name        = "Arval"
  resource_group_name = "prod-apigate-rg"
  # Known issue relate to azurerm_api_management_api.id property https://github.com/hashicorp/terraform-provider-azurerm/issues/25020
  api_id        = replace(azurerm_api_management_api.arval-staging-api.id, "/;rev=[^/]+/", "")
  allow_tracing = false
  state         = "active"
  user_id       = null
  product_id    = null
}
