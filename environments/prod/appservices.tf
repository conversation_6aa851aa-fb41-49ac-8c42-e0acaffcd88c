module "oneview_backend_app" {
  source = "../../modules/appservice/linux"

  name                       = "oneview-backend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  location                   = var.location
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id

  container_registry_id = var.container_registry_id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"                          = "Production"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE"             = "false"
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0"
    "APPLICATIONINSIGHTS_CONFIGURATION_CONTENT"       = null
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=7b178d14-7aa0-4e23-a44f-713d1b16403b;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=ecf6b643-4956-4f4f-adec-fbbdba175e79"
    "ApplicationInsightsAgent_EXTENSION_VERSION"      = "~3"
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }
  custom_domains = [
    "api.oneview.gba4pl.com"
  ]
  cloudflare_zone_id = "b672e91867e9531256869fb2c877ab9e"
  use_key_vault      = true
  key_vault_name     = "kv-jyvcbamwubifh"

  virtual_network_enabled               = true
  virtual_network_subnet_id             = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
  key_vault_network_acls_default_action = "Deny"
  key_vault_network_acls_virtual_network_subnet_ids = [
    "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
  ]

  tags = merge(
    local.common_tags,
    {
      "Accessibility"       = "Internal,On-premise,External,Public"
      "Application"         = "Not-Set"
      "BusinessCriticality" = "Low/Medium/High/Critical"
      "BusinessUnit"        = "Not-Set"
      "CostCenter"          = "Not-Set"
      "DataSensitivity"     = "General"
      "Department"          = "Not-Set"
      "Environment"         = "prod"
      "ExpirationDate"      = "Not-Set-Use-YYYY-MM-DD"
      "Owner"               = "<EMAIL>"
      "Project"             = "Not-Set"
      "Sites"               = "Global"
      "Usage"               = "PROD2025/CanBeRemoved/NeedsReview"
    }
  )
}

module "oneview_frontend_app" {
  source = "../../modules/appservice/linux"

  name                       = "oneview-frontend"
  environment                = var.environment
  client_affinity_enabled    = false
  resource_group_name        = azurerm_resource_group.app.name
  location                   = var.location
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id

  container_registry_id = var.container_registry_id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"              = "Production"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
    "WEBSITES_PORT"                       = "3000"
  }
  custom_domains = [
    "oneview.gba4pl.com" # There is an working production domain for this, so use this one instead of oneview.gbagroup.com, will change back when the old one retires
  ]
  cloudflare_zone_id = "b672e91867e9531256869fb2c877ab9e"
  use_key_vault      = false

  tags = local.common_tags
}

module "tms_backend_app" {
  source = "../../modules/appservice/linux"

  name                       = "tms-backend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  custom_domains             = ["api.tms.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"
  container_registry_id      = var.container_registry_id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"                          = "Production"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE"             = "false"
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0",
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0",
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=7b178d14-7aa0-4e23-a44f-713d1b16403b;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=ecf6b643-4956-4f4f-adec-fbbdba175e79",
    "ApplicationInsightsAgent_EXTENSION_VERSION"      = "~3",
    "DiagnosticServices_EXTENSION_VERSION"            = "~3",
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled",
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled",
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled",
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended",
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }
  use_key_vault  = true
  key_vault_name = "kv-tms-vault"

  virtual_network_enabled               = true
  key_vault_network_acls_default_action = "Allow"
  key_vault_network_acls_virtual_network_subnet_ids = [
    "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
  ]

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}

module "job_service_app" {
  source = "../../modules/appservice/linux"

  name                       = "job-service"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  location                   = var.location
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id

  container_registry_id = var.container_registry_id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"              = "Production"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
    "APPINSIGHTS_PROFILERFEATURE_VERSION" = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION" = "1.0.0"
    "KEYVAULT_NAME"                       = "prod-kv-job-service"
    # "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=7b178d14-7aa0-4e23-a44f-713d1b16403b;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=ecf6b643-4956-4f4f-adec-fbbdba175e79"
    "ApplicationInsightsAgent_EXTENSION_VERSION"      = "~3"
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }
  custom_domains = []
  use_key_vault  = true
  key_vault_name = "kv-job-service"

  virtual_network_enabled               = true
  virtual_network_subnet_id             = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
  key_vault_network_acls_default_action = "Deny"
  key_vault_network_acls_virtual_network_subnet_ids = [
    "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/prod-asp-snet"
  ]

  tags = merge(
    local.common_tags,
    {
      "Accessibility"       = "Internal,On-premise"
      "Application"         = "Not-Set"
      "BusinessCriticality" = "High"
      "BusinessUnit"        = "Not-Set"
      "CostCenter"          = "Not-Set"
      "DataSensitivity"     = "General"
      "Department"          = "Not-Set"
      "Environment"         = "prod"
      "ExpirationDate"      = "Not-Set-Use-YYYY-MM-DD"
      "Owner"               = "<EMAIL>"
      "Project"             = "Not-Set"
      "Sites"               = "Global"
      "Usage"               = "PROD2025/CanBeRemoved/NeedsReview"
    }
  )
}
