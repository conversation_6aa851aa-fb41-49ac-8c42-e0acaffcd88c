module "foresight_fe_custom_domain" {
  source = "../../modules/appservice/custom-domain"

  domain_name                   = "foresight-be.gbatechnologies.net"
  app_service_name              = "prod-foresight-be"
  resource_group_name           = "prod-foresight-rg"
  cloudflare_zone_id            = "f9919cce5fbe55edc69a584e174499dd"
  custom_domain_verification_id = "52202A17758C2FAA98A11175BED342C692A7F41499F667C54D82777B8ABA1B03"
  app_service_default_hostname  = "prod-foresight-be.azurewebsites.net"

  tags = {
    Accessibility       = "Internal,On-premise,External,Public"
    Application         = "Not-Set"
    BusinessCriticality = "Low/Medium/High/Critical"
    BusinessUnit        = "Not-Set"
    CostCenter          = "Not-Set"
    CreatedBy           = "Unknown"
    DataSensitivity     = "General"
    Department          = "Not-Set"
    Environment         = "prod"
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "Not-Set"
    Sites               = "Global"
    Usage               = "PROD2025/CanBeRemoved/NeedsReview"
  }
}
