# ATC01 Route Rules

# VLAN 101 - Data
resource "azurerm_route" "atc01_1" {
  name                   = "ATC01-10-1-1-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 102 - Voice
resource "azurerm_route" "atc01_2" {
  name                   = "ATC01-10-1-2-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 103 - CCTV
resource "azurerm_route" "atc01_3" {
  name                   = "ATC01-10-1-3-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 104 - Access Control
resource "azurerm_route" "atc01_4" {
  name                   = "ATC01-10-1-4-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 105 - Audio Visual
resource "azurerm_route" "atc01_5" {
  name                   = "ATC01-10-1-5-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 106 - WiFi 01
resource "azurerm_route" "atc01_6" {
  name                   = "ATC01-10-1-6-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 107 - WiFi 02
resource "azurerm_route" "atc01_7" {
  name                   = "ATC01-10-1-7-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 108 - MGMT
resource "azurerm_route" "atc01_8" {
  name                   = "ATC01-10-1-8-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 109 - Spare 01 (Reserved)
resource "azurerm_route" "atc01_9" {
  name                   = "ATC01-10-1-9-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 110 - Spare 02 (Reserved)
resource "azurerm_route" "atc01_10" {
  name                   = "ATC01-10-1-10-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
