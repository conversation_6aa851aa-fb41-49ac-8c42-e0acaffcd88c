# Azure Redis Cache resource for development environment
resource "azurerm_redis_cache" "redis_oneview" {
  # Basic resource information
  name                = "${var.environment}-redis-oneview"  # Naming convention follows environment
  location            = azurerm_resource_group.app.location # UK West region for data residency
  resource_group_name = azurerm_resource_group.app.name

  # SKU configuration - Basic C2 provides good balance for dev environment
  sku_name = "Standard"
  family   = "C"
  capacity = 2

  # Security configurations
  non_ssl_port_enabled = false # Disable non-SSL port for enhanced security
  minimum_tls_version  = "1.2" # Enforce minimum TLS 1.2 for connections

  public_network_access_enabled = true # Enable public access for dev environment

  # Redis memory management configurations
  redis_configuration {
    maxmemory_reserved              = "299" # Memory reserved for non-cache operations
    maxfragmentationmemory_reserved = "299" # Memory reserved for fragmentation
    maxmemory_delta                 = "299" # Additional memory for overhead
  }

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags["CreatedAt"],
    ]
  }
}

output "hostname" {
  value = azurerm_redis_cache.redis_oneview.hostname
}

output "ssl_port" {
  value = azurerm_redis_cache.redis_oneview.ssl_port
}

output "primary_access_key" {
  value     = azurerm_redis_cache.redis_oneview.primary_access_key
  sensitive = true
}

output "secondary_access_key" {
  value     = azurerm_redis_cache.redis_oneview.secondary_access_key
  sensitive = true
}

output "primary_connection_string" {
  value     = azurerm_redis_cache.redis_oneview.primary_connection_string
  sensitive = true
}

output "secondary_connection_string" {
  value     = azurerm_redis_cache.redis_oneview.secondary_connection_string
  sensitive = true
}