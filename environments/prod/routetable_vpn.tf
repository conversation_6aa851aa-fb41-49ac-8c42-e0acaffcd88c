# VPN Users Route Rules

# VPN Route for **********/23 network
resource "azurerm_route" "vpn_network_172_21" {
  name                   = "VPN-172-21-0-0-23"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "**********/23"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VPN Route for **********/23 network
resource "azurerm_route" "vpn_network_172_22" {
  name                   = "VPN-172-22-0-0-23"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "**********/23"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
