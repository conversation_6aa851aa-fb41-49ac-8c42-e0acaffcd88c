# ----------------- APIM section | Dev API -----------------

resource "azurerm_api_management_api" "dev-api" {
  name                = "dev-api"
  resource_group_name = "prod-apigate-rg"
  api_management_name = "prod-gbagateways-apim"
  revision            = "1"
  display_name        = "Dev API"
  path                = "dev"
  protocols           = ["https"]
}

resource "azurerm_api_management_api_policy" "dev-policy" {
  api_name            = azurerm_api_management_api.dev-api.name
  api_management_name = azurerm_api_management_api.dev-api.api_management_name
  resource_group_name = azurerm_api_management_api.dev-api.resource_group_name

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <!-- Extract variables from context -->
        <set-variable name="Name" value="@(context.Subscription.Name)" />
        <!-- The X-Original-URL header here is /dev/headers like, -->
        <!-- ... But the function app will see a different URL from the same header name, values like /api/headers -->
        <set-variable name="OriginalUrl" value="@(context.Request.Headers.GetValueOrDefault("X-Original-URL", string.Empty))" />
        <set-header name="Client-Identity-Name" exists-action="override">
            <value>@(context.Variables.GetValueOrDefault<string>("Name"))</value>
        </set-header>
        <!-- Path-based allow list mapping using choose for clarity and maintainability -->
        <choose>
            <when condition="@(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;OriginalUrl&quot;) == &quot;/dev/headers&quot;)">
                <set-variable name="AllowList" value="Arval" />
            </when>
            <when condition="@(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;OriginalUrl&quot;) == &quot;/dev/new&quot;)">
                <set-variable name="AllowList" value="Arval" />
            </when>
            <when condition="@(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;OriginalUrl&quot;) == &quot;/dev/vehicles&quot;)">
                <set-variable name="AllowList" value="Engineius" />
            </when>
            <when condition="@(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;OriginalUrl&quot;) == &quot;/dev/job-service-health&quot;)">
                <set-variable name="AllowList" value="Arval" />
            </when>
            <otherwise>
                <set-variable name="AllowList" value="3" />
            </otherwise>
        </choose>
        <!-- Block unauthorized access -->
        <choose>
            <when condition="@(!((context.Variables.GetValueOrDefault&lt;string&gt;(&quot;AllowList&quot;) ?? &quot;&quot;).Split(',').Contains(context.Variables.GetValueOrDefault&lt;string&gt;(&quot;Name&quot;))))">
                <return-response>
                    <set-status code="403" reason="Forbidden" />
                    <set-header name="Content-Type" exists-action="override">
                        <value>text/plain</value>
                    </set-header>
                    <set-body>Hey, do you have permission to access this API?</set-body>
                </return-response>
            </when>
        </choose>
        <!-- Log request (only for POST requests) -->
        <choose>
            <when condition="@(context.Request.Method == "POST")">
                <send-request mode="copy" response-variable-name="authResult" timeout="20" ignore-error="false">
                    <set-url>https://dev-apigate-func.azurewebsites.net/api/log-request</set-url>
                    <set-method>POST</set-method>
                    <set-header name="x-functions-key" exists-action="override">
                        <value>${data.azurerm_function_app_host_keys.dev_apigate_func_keys.default_function_key}</value>
                    </set-header>
                    <set-body>@(context.Request.Body.As<string>(preserveContent: true))</set-body>
                </send-request>
            </when>
        </choose>
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}

# ----------------- APIM section | Dev API | Operation: Check-in -----------------

resource "azurerm_api_management_api_operation" "dev-operation-checkin" {
  operation_id        = "dev-operation-checkin"
  api_name            = azurerm_api_management_api.dev-api.name
  api_management_name = "prod-gbagateways-apim"
  resource_group_name = "prod-apigate-rg"
  display_name        = "Check-in"
  method              = "POST"
  url_template        = "/new"
}

resource "azurerm_api_management_api_operation_policy" "dev-operation-checkin-policy" {
  api_name            = azurerm_api_management_api_operation.dev-operation-checkin.api_name
  api_management_name = azurerm_api_management_api_operation.dev-operation-checkin.api_management_name
  resource_group_name = azurerm_api_management_api_operation.dev-operation-checkin.resource_group_name
  operation_id        = azurerm_api_management_api_operation.dev-operation-checkin.operation_id

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <set-backend-service id="apim-generated-policy" backend-id="${azurerm_api_management_backend.dev-apigate-func.name}" />
        <rewrite-uri template="/check-in" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}

# ----------------- APIM section | Dev API | Operation: Headers -----------------

resource "azurerm_api_management_api_operation" "dev-operation-headers" {
  operation_id        = "dev-operation-headers"
  api_name            = azurerm_api_management_api.dev-api.name
  api_management_name = "prod-gbagateways-apim"
  resource_group_name = "prod-apigate-rg"
  display_name        = "Headers"
  method              = "GET"
  url_template        = "/headers"
}

resource "azurerm_api_management_api_operation_policy" "dev-operation-headers-policy" {
  api_name            = azurerm_api_management_api_operation.dev-operation-headers.api_name
  api_management_name = azurerm_api_management_api_operation.dev-operation-headers.api_management_name
  resource_group_name = azurerm_api_management_api_operation.dev-operation-headers.resource_group_name
  operation_id        = azurerm_api_management_api_operation.dev-operation-headers.operation_id

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <set-backend-service id="apim-generated-policy" backend-id="${azurerm_api_management_backend.dev-apigate-func.name}" />
        <rewrite-uri template="/headers" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}

# ----------------- APIM section | Dev API | Operation: Transport-Vehicles -----------------

resource "azurerm_api_management_api_operation" "dev-operation-transport-vehicles" {
  operation_id        = "dev-operation-transport-vehicles"
  api_name            = azurerm_api_management_api.dev-api.name
  api_management_name = "prod-gbagateways-apim"
  resource_group_name = "prod-apigate-rg"
  display_name        = "Transport-Vehicles"
  method              = "GET"
  url_template        = "/vehicles"
}

resource "azurerm_api_management_api_operation_policy" "dev-operation-transport-vehicles-policy" {
  api_name            = azurerm_api_management_api_operation.dev-operation-transport-vehicles.api_name
  api_management_name = azurerm_api_management_api_operation.dev-operation-transport-vehicles.api_management_name
  resource_group_name = azurerm_api_management_api_operation.dev-operation-transport-vehicles.resource_group_name
  operation_id        = azurerm_api_management_api_operation.dev-operation-transport-vehicles.operation_id

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <set-backend-service id="apim-generated-policy" backend-id="${azurerm_api_management_backend.dev-apigate-func.name}" />
        <rewrite-uri template="/transport" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}

# ----------------- APIM section | Dev API | Operation: JobService Health -----------------

resource "azurerm_api_management_api_operation" "dev-operation-job-service-health" {
  operation_id        = "dev-operation-job-service-health"
  api_name            = azurerm_api_management_api.dev-api.name
  api_management_name = "prod-gbagateways-apim"
  resource_group_name = "prod-apigate-rg"
  display_name        = "JobService Health"
  method              = "GET"
  url_template        = "/job-service-health"
}

resource "azurerm_api_management_api_operation_policy" "dev-operation-job-service-health-policy" {
  api_name            = azurerm_api_management_api_operation.dev-operation-job-service-health.api_name
  api_management_name = azurerm_api_management_api_operation.dev-operation-job-service-health.api_management_name
  resource_group_name = azurerm_api_management_api_operation.dev-operation-job-service-health.resource_group_name
  operation_id        = azurerm_api_management_api_operation.dev-operation-job-service-health.operation_id

  xml_content = <<XML
<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <set-backend-service id="apim-generated-policy" backend-id="${azurerm_api_management_backend.dev-apigate-func.name}" />
        <rewrite-uri template="/job-service-health" />
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>
XML
}
