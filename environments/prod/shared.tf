resource "azurerm_resource_group" "prod_shared_rg" {
  name     = "prod-shared-rg"
  location = "uksouth"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_service_plan" "shared_linux_asp" {
  name                = "prod-shared-asp"
  resource_group_name = azurerm_resource_group.prod_shared_rg.name
  location            = azurerm_resource_group.prod_shared_rg.location
  # os_type can be `Windows`, `Linux`, and `WindowsContainer`
  os_type = "Linux"
  # SKU names https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/service_plan#sku_name-1
  # Pricing tier https://azure.microsoft.com/en-us/pricing/details/app-service/linux/

  sku_name     = "P0v3"
  worker_count = 1

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}
