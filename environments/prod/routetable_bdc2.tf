# BDC2 Route Rules

# VLAN 101 - Data
resource "azurerm_route" "bdc2_data" {
  name                   = "BDC2-Data-10-11-1-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 102 - Voice
resource "azurerm_route" "bdc2_voice" {
  name                   = "BDC2-Voice-10-11-2-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 103 - CCTV
resource "azurerm_route" "bdc2_cctv" {
  name                   = "BDC2-CCTV-10-11-3-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 104 - Access Control
resource "azurerm_route" "bdc2_access_control" {
  name                   = "BDC2-AccessControl-10-11-4-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 105 - Audio Visual
resource "azurerm_route" "bdc2_audio_visual" {
  name                   = "BDC2-AudioVisual-10-11-5-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 106 - WiFi 01
resource "azurerm_route" "bdc2_wifi01" {
  name                   = "BDC2-WiFi01-10-11-6-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 107 - WiFi 02
resource "azurerm_route" "bdc2_wifi02" {
  name                   = "BDC2-WiFi02-10-11-7-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 108 - MGMT
resource "azurerm_route" "bdc2_mgmt" {
  name                   = "BDC2-MGMT-10-11-8-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 109 - Spare 01 (Reserved)
resource "azurerm_route" "bdc2_spare01" {
  name                   = "BDC2-Spare01-10-11-9-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 110 - Spare 02 (Reserved)
resource "azurerm_route" "bdc2_spare02" {
  name                   = "BDC2-Spare02-10-11-10-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "**********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
