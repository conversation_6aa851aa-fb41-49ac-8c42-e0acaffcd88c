# Pipeline preparation

1. Assign the container registry ID to the `container_registry_id` variable by setting the `TF_VAR_container_registry_id` environment variable.
2. Assign the dev environment's ARM client ID, secret, and tenant ID to the `DEV_ARM_CLIENT_ID`, `DEV_ARM_CLIENT_SECRET`, and `DEV_ARM_TENANT_ID` environment variables.
3. Assign the prod environment's ARM client ID, secret, and tenant ID to the `ARM_CLIENT_ID`, `ARM_CLIENT_SECRET`, and `ARM_TENANT_ID` environment variables.