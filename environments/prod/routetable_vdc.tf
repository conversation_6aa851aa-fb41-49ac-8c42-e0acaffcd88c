# VDC Route Rules

# VLAN 101 - Data
resource "azurerm_route" "vdc_data" {
  name                   = "VDC-Data-10-4-1-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 102 - Voice
resource "azurerm_route" "vdc_voice" {
  name                   = "VDC-Voice-10-4-2-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 103 - CCTV
resource "azurerm_route" "vdc_cctv" {
  name                   = "VDC-CCTV-10-4-3-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 104 - Access Control
resource "azurerm_route" "vdc_access_control" {
  name                   = "VDC-AccessControl-10-4-4-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 105 - Audio Visual
resource "azurerm_route" "vdc_audio_visual" {
  name                   = "VDC-AudioVisual-10-4-5-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 106 - WiFi 01
resource "azurerm_route" "vdc_wifi01" {
  name                   = "VDC-WiFi01-10-4-6-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 107 - WiFi 02
resource "azurerm_route" "vdc_wifi02" {
  name                   = "VDC-WiFi02-10-4-7-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 108 - MGMT
resource "azurerm_route" "vdc_mgmt" {
  name                   = "VDC-MGMT-10-4-8-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 109 - Spare 01 (Reserved)
resource "azurerm_route" "vdc_spare01" {
  name                   = "VDC-Spare01-10-4-9-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 110 - Spare 02 (Reserved)
resource "azurerm_route" "vdc_spare02" {
  name                   = "VDC-Spare02-10-4-10-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
