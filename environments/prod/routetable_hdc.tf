# HDC Route Rules

# VLAN 101 - Data
resource "azurerm_route" "hdc_data" {
  name                   = "HDC-Data-10-12-1-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 102 - Voice
resource "azurerm_route" "hdc_voice" {
  name                   = "HDC-Voice-10-12-2-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 103 - CCTV
resource "azurerm_route" "hdc_cctv" {
  name                   = "HDC-CCTV-10-12-3-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 104 - Access Control
resource "azurerm_route" "hdc_access_control" {
  name                   = "HDC-AccessControl-10-12-4-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 105 - Audio Visual
resource "azurerm_route" "hdc_audio_visual" {
  name                   = "HDC-AudioVisual-10-12-5-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 106 - WiFi 01
resource "azurerm_route" "hdc_wifi01" {
  name                   = "HDC-WiFi01-10-12-6-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 107 - WiFi 02
resource "azurerm_route" "hdc_wifi02" {
  name                   = "HDC-WiFi02-10-12-7-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 108 - MGMT
resource "azurerm_route" "hdc_mgmt" {
  name                   = "HDC-MGMT-10-12-8-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 109 - Spare 01 (Reserved)
resource "azurerm_route" "hdc_spare01" {
  name                   = "HDC-Spare01-10-12-9-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "*********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}

# VLAN 110 - Spare 02 (Reserved)
resource "azurerm_route" "hdc_spare02" {
  name                   = "HDC-Spare02-10-12-10-0-24"
  resource_group_name    = "prod-meraki-rg"
  route_table_name       = azurerm_route_table.main.name
  address_prefix         = "**********/24"
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = "**********"
}
