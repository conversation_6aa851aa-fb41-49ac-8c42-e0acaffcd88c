module "oneview_backend_app" {
  source = "../../modules/appservice/linux"

  name                       = "oneview-backend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  custom_domains             = ["dev.api.oneview.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"

  container_registry_id = module.container_registry.id
  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"                          = "DevNew"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE"             = "false"
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0"
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=36aa292a-6704-4fe8-8497-f9a14ee58f3e;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=ef4bdcd9-43a3-41af-ba19-9d1e38faa407"
    "ApplicationInsightsAgent_EXTENSION_VERSION"      = "~3"
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
  }

  virtual_network_enabled   = true
  virtual_network_subnet_id = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/dev-asp-linux-plan-snet"

  use_key_vault                         = true
  key_vault_name                        = "kv-ldmbkqzvhftmr"
  key_vault_network_acls_default_action = "Deny"
  key_vault_network_acls_ip_rules = [
    "**************/32",
    "************/24",
  ]
  key_vault_network_acls_virtual_network_subnet_ids = [
    "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/dev-asp-linux-plan-snet",
  ]

  health_check_path                 = "/health"
  health_check_eviction_time_in_min = 5

  tags = merge(local.common_tags, {
    Accessibility       = "Internal",
    Application         = "OneView Backend",
    BusinessCriticality = "Medium",
    CostCenter          = "Not-Set",
    CreatedBy           = "Terraform",
    Environment         = "dev",
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD",
    KeyVaultFor         = "dev-oneview-backend",
    Owner               = "<EMAIL>",
    Project             = "OneView",
    Usage               = "Development",
  })
  depends_on = [azurerm_resource_group.app]
}

module "vwfs_dev_fresh" {
  source = "../../modules/appservice/linux"

  name                       = "vwfs-fresh"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location

  environment_variables = {
    "ASPNETCORE_ENVIRONMENT"              = "Dev"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
    "ASPNETCORE_FORWARDEDHEADERS_ENABLED" = "true"
  }

  virtual_network_enabled   = true
  virtual_network_subnet_id = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/dev-asp-linux-plan-snet"

  container_registry_id = module.container_registry.id
  use_key_vault         = true
  key_vault_name        = "kv-uhdclozyxvosk"

  tags = merge(local.common_tags, {
    CreatedBy   = "Terraform",
    Environment = "dev",
    KeyVaultFor = "dev-tms-backend",
    OwnedBy     = "<EMAIL>"
  })
  depends_on = [azurerm_resource_group.app]
}

module "oneview_frontend_app" {
  source = "../../modules/appservice/linux"

  name                       = "oneview-frontend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  client_affinity_enabled    = false
  custom_domains             = ["dev.oneview.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"
  container_registry_id      = module.container_registry.id
  environment_variables = {
    "WEBSITES_PORT"                       = "3000"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
  }
  use_key_vault = false

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}

module "tms_backend_app" {
  source = "../../modules/appservice/linux"

  name                       = "tms-backend"
  environment                = var.environment
  resource_group_name        = azurerm_resource_group.app.name
  service_plan_id            = azurerm_service_plan.app_linux_plan.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  location                   = azurerm_resource_group.app.location
  custom_domains             = ["dev.api.tms.gba4pl.com"]
  cloudflare_zone_id         = "b672e91867e9531256869fb2c877ab9e"
  container_registry_id      = module.container_registry.id

  virtual_network_enabled   = true
  virtual_network_subnet_id = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/dev-asp-linux-plan-snet"

  environment_variables = {
    "APPINSIGHTS_INSTRUMENTATIONKEY"                  = "8c8c60eb-085a-4202-866a-be54460d87b4"
    "APPINSIGHTS_PROFILERFEATURE_VERSION"             = "1.0.0"
    "APPINSIGHTS_SNAPSHOTFEATURE_VERSION"             = "1.0.0"
    "APPLICATIONINSIGHTS_CONNECTION_STRING"           = "InstrumentationKey=8c8c60eb-085a-4202-866a-be54460d87b4;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=0c732fdd-fcab-4db4-b7f1-b51abd8c5e6c"
    "ApplicationInsightsAgent_EXTENSION_VERSION"      = "~3"
    "DiagnosticServices_EXTENSION_VERSION"            = "~3"
    "InstrumentationEngine_EXTENSION_VERSION"         = "disabled"
    "SnapshotDebugger_EXTENSION_VERSION"              = "disabled"
    "XDT_MicrosoftApplicationInsights_BaseExtensions" = "disabled"
    "XDT_MicrosoftApplicationInsights_Mode"           = "recommended"
    "XDT_MicrosoftApplicationInsights_PreemptSdk"     = "disabled"
    "ASPNETCORE_ENVIRONMENT"                          = "DevNew"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE"             = "false"
  }

  use_key_vault                         = true
  key_vault_network_acls_default_action = "Deny"
  key_vault_name                        = "kv-tms-vault"
  key_vault_network_acls_ip_rules = [
    "*************/32",
  ]
  key_vault_network_acls_virtual_network_subnet_ids = [
    "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/dev-asp-linux-plan-snet",
    "/subscriptions/d40f4d52-4fce-4731-be1a-572bb2cdc92e/resourceGroups/dev-rg-app/providers/Microsoft.Network/virtualNetworks/dev-uks-vnet/subnets/dev-uks-snet-backend",
  ]

  tags = merge(local.common_tags, {
    Accessibility       = "Public",
    Application         = "TMS",
    BusinessCriticality = "High",
    BusinessUnit        = null,
    CostCenter          = "Not-Set",
    DataSensitivity     = null,
    Department          = null,
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD",
    Owner               = "<EMAIL>",
    Project             = "TMS",
    Sites               = null,
    Usage               = "Development",
  })
  depends_on = [azurerm_resource_group.app]
}
