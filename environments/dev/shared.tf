# Create a Resource Group
resource "azurerm_resource_group" "dev_shared_rg" {
  name     = "${var.environment}-shared-rg"
  location = var.location
}

# Create a Storage Account
resource "azurerm_storage_account" "shared" {
  name                     = "${var.environment}stshared"
  resource_group_name      = azurerm_resource_group.dev_shared_rg.name
  location                 = azurerm_resource_group.dev_shared_rg.location
  account_kind             = "StorageV2"
  account_tier             = "Standard"
  account_replication_type = "LRS"
  min_tls_version          = "TLS1_2"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags["CreatedAt"],
    ]
  }
}

# Create Private DNS Zone for Azure SQL Database private endpoints
resource "azurerm_private_dns_zone" "database_private_dns_zone" {
  name                = "privatelink.database.windows.net"
  resource_group_name = azurerm_resource_group.dev_shared_rg.name

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags["CreatedAt"],
    ]
  }
}

# Create a Blob Container for BACPAC backups
resource "azurerm_storage_container" "bacpac" {
  name                  = "bacpac-backups"
  storage_account_id    = azurerm_storage_account.shared.id
  container_access_type = "private"
}
