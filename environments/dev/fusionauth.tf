# Please note
# Since FusionAuth is an Auth service, it is not necessary to deploy multiple environments.
# Therefore, we will only deploy one instance of FusionAuth in the dev environment.

// Network Security Group for VM
resource "azurerm_network_security_group" "vm_nsg" {
  # Name should follow pattern: <environment>-<app>-nsg per naming convention
  name                = "${var.environment}-nsg-vm-fusionauth"
  location            = azurerm_resource_group.app.location
  resource_group_name = azurerm_resource_group.app.name

  security_rule {
    name                       = "SSH"
    priority                   = 100
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "9374"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

// Subnet for FusionAuth
resource "azurerm_subnet" "fusionauth_subnet" {
  # Name should follow pattern: <environment>-<app>-snet per naming convention
  name                 = "${var.environment}-snet-fusionauth"
  resource_group_name  = azurerm_resource_group.app.name
  virtual_network_name = module.networking_app.vnet_name
  address_prefixes     = ["**********/24"]
}

// Public IP for VM
resource "azurerm_public_ip" "vm_pip" {
  # Name should follow pattern: <environment>-<app>-pip per naming convention
  name                = "${var.environment}-pip-vm-fusionauth"
  resource_group_name = azurerm_resource_group.app.name
  location            = azurerm_resource_group.app.location
  allocation_method   = "Static"
  sku                 = "Standard"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

// Network Interface for VM
resource "azurerm_network_interface" "vm_nic" {
  # Name should follow pattern: <environment>-<app>-nic per naming convention
  name                = "${var.environment}-nic-vm-fusionauth"
  location            = azurerm_resource_group.app.location
  resource_group_name = azurerm_resource_group.app.name

  ip_configuration {
    name                          = "internal"
    subnet_id                     = azurerm_subnet.fusionauth_subnet.id
    private_ip_address_allocation = "Dynamic"
    public_ip_address_id          = azurerm_public_ip.vm_pip.id
  }

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

// Associate NSG with NIC
resource "azurerm_network_interface_security_group_association" "vm_nsg_association" {
  network_interface_id      = azurerm_network_interface.vm_nic.id
  network_security_group_id = azurerm_network_security_group.vm_nsg.id
}

// Linux VM
resource "azurerm_linux_virtual_machine" "vm_fusionauth" {
  # Name should follow pattern: <environment>-<app>-vm per naming convention
  name                  = "${var.environment}-vm-fusionauth"
  resource_group_name   = azurerm_resource_group.app.name
  location              = azurerm_resource_group.app.location
  size                  = "Standard_D2s_v3"
  admin_username        = "adminuser"
  patch_assessment_mode = "AutomaticByPlatform"

  network_interface_ids = [
    azurerm_network_interface.vm_nic.id
  ]

  custom_data = base64encode(<<-EOF
    #!/bin/bash
    # Backup original sshd_config
    sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak
    
    # Change SSH port
    sudo sed -i 's/#Port 22/Port 9374/' /etc/ssh/sshd_config
    
    # Restart SSH service
    sudo systemctl restart sshd
    EOF
  )

  admin_ssh_key {
    username   = "adminuser"
    public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCi9g1gqvJvrOBgy89A+3d3YZCaWCKDChHldRyYqt97LBr8tLAlGEYEpwPmbKpci0TLfry76Y7WWZyqisay2v1ePJgvNeiZtlD+A8nEtmZ/dXXSE2YgwmwNCOri/5tsFilTPLj0sj+JnowWGgueIez6Sijp+F8DDgprex65KCqKctKGo0DFinMMfiEIV1vcEwJTCjz9tRid3268FWmU5+JIk5FwEU3cZT/sHZGySPchRK+HkWFVGH/XmTy0GAnrYLTy9gWh6iFKNJiEJ8mRV9PM+jR37MH0NFWOonEZBanPFqRdiYE/5Mod/Bfqc4oHvU6OfXpE/EsReorUsdErN01/vIFWgUfHHZu9MKJZdnLH367OvXZkkHACbl4QL8mbFqy4LY1EXSGAl0fCYy/KyMgUmtg8gBk6P4LU/g1knttJn2ZnJ8rbsQkxPPBgMDKwMVEV1bhIwtVKXnePRDmJ2x2aQG1V8O1BzABLRuZY8eWlBKz+RCSFjNvCgx2VXb1YS5MLCf7eLVECu9BavoTqZExGGC7Y2RIqHrSn2sHd40Ogb3vNIjdgyumKi+NgH8dxQxF9D2StWt/Ncc/R4aDYCLm4F2UtG2NfTWuH/scERWHHEa5HHwzg+nz+9oWaUzD7XjkG2T6REmTCLyxfotAs6wscKd7vwBkvfCzqoW6rlDnwrQ== <EMAIL>"
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Premium_LRS"
    disk_size_gb         = 100
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "0001-com-ubuntu-server-focal"
    sku       = "20_04-lts"
    version   = "latest"
  }

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}
