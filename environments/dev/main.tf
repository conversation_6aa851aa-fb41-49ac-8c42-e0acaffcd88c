provider "azurerm" {
  features {}
}

provider "cloudflare" {
}

locals {
  common_tags = {
    CreatedBy   = "Terraform"
    CreatedAt   = timestamp()
    OwnedBy     = "<EMAIL>"
    Environment = var.environment
  }
}

data "azurerm_client_config" "current" {}

resource "azurerm_resource_group" "app" {
  name     = format("%s-rg-app", var.environment)
  location = var.location
  tags     = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_log_analytics_workspace" "log_analytics_workspace" {
  name                = format("%s-log-analytics-workspace", var.environment)
  location            = azurerm_resource_group.app.location
  resource_group_name = azurerm_resource_group.app.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
  tags                = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_service_plan" "app_windows_plan" {
  name                = "${var.environment}-asp-windows-plan"
  resource_group_name = azurerm_resource_group.app.name
  location            = azurerm_resource_group.app.location
  # os_type can be `Windows`, `Linux`, and `WindowsContainer`
  os_type = "Windows"
  # SKU names https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/service_plan#sku_name-1
  sku_name     = "D1"
  worker_count = 1

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_service_plan" "app_linux_plan" {
  # Name should follow pattern: <environment>-asp per naming convention, as this is a shared resource
  name                = "${var.environment}-asp-linux-plan"
  resource_group_name = azurerm_resource_group.app.name
  location            = azurerm_resource_group.app.location
  # os_type can be `Windows`, `Linux`, and `WindowsContainer`
  os_type = "Linux"
  # SKU names https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/service_plan#sku_name-1
  sku_name = "B2"

  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

module "networking_app" {
  source = "../../modules/networking"

  address_space       = ["**********/16"]
  environment         = var.environment
  location_short      = var.location_short
  resource_group_name = azurerm_resource_group.app.name
  subnets = [
    {
      address_prefix = "**********/24"
      name           = format("%s-%s-snet-backend", var.environment, var.location_short)
      service_endpoints = [
        "Microsoft.KeyVault",
      ]
      delegation = {
        name = "delegation"
        service_delegation = {
          name    = "Microsoft.ContainerInstance/containerGroups"
          actions = ["Microsoft.Network/virtualNetworks/subnets/action"]
        }
      }
    },
    {
      address_prefix = "**********/23"
      name           = format("%s-containerapps-snet", var.environment)
      delegation = {
        name = "delegation"
        service_delegation = {
          name    = "Microsoft.App/environments"
          actions = ["Microsoft.Network/virtualNetworks/subnets/join/action"]
        }
      }
      service_endpoints = [
        "Microsoft.Sql"
      ]
    },
    {
      address_prefix = "**********/24"
      name           = format("%s-appservice-snet", var.environment)
      delegation = {
        name = "delegation"

        service_delegation = {
          actions = [
            "Microsoft.Network/virtualNetworks/subnets/join/action",
          ]
          name = "Microsoft.App/environments"
        }
      }
    },
    {
      address_prefix = "**********/24"
      name           = format("%s-containerapps-pe-snet", var.environment)
    },
    # Dedicated subnet for private endpoints serving the entire virtual network
    {
      address_prefix = "**********/24",
      name           = format("%s-pe-snnet", var.environment)
    }
  ]

  tags       = local.common_tags
  depends_on = [azurerm_resource_group.app]
}

module "container_registry" {
  source = "../../modules/container-registry"

  name                = "gbagroup"
  resource_group_name = azurerm_resource_group.app.name
  sku                 = "Standard"
  admin_enabled       = true

  tags = local.common_tags
}
