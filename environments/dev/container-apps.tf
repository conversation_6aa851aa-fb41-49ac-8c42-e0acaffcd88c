module "container_environment" {
  source = "../../modules/container-app-environment"

  name                       = "${var.environment}-app-capp"
  resource_group_name        = azurerm_resource_group.app.name
  location                   = azurerm_resource_group.app.location
  infrastructure_subnet_id   = module.networking_app.subnet_ids["${var.environment}-containerapps-snet"]
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  container_registry_id      = var.container_registry_id
  enable_private_endpoint    = false # Keeping simple for dev

  depends_on = [
    module.networking_app
  ]
  tags = merge(local.common_tags, {
    "Accessibility"       = "Internal,On-premise,External,Public"
    "Application"         = "Not-Set"
    "BusinessCriticality" = "Low/Medium/High/Critical"
    "CostCenter"          = "Not-Set"
    "CreatedAt"           = "2025-07-03T07:33:08Z"
    "CreatedBy"           = "Terraform"
    "CreationDate"        = "2025-07-03"
    "Environment"         = "dev"
    "ExpirationDate"      = "Not-Set-Use-YYYY-MM-DD"
    "OwnedBy"             = "<EMAIL>"
    "Owner"               = "<EMAIL>"
    "Project"             = "Not-Set"
    "Usage"               = "DEV2025/CanBeRemoved/NeedsReview"
  })
}
