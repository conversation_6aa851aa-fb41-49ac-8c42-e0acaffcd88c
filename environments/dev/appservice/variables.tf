variable "name" {
  description = "The name of the Linux Web App"
  type        = string
}

variable "environment" {
  description = "The environment name (e.g., dev, test, prod)"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The Azure region where resources will be created"
  type        = string
}

variable "service_plan_id" {
  description = "The ID of the existing App Service Plan"
  type        = string
}

variable "tags" {
  description = "A map of tags to assign to the resources"
  type        = map(string)
  default     = {}
}

variable "docker_registry_url" {
  description = "The URL of the Docker registry"
  type        = string
  default     = "https://gbagroup.azurecr.io"
}

variable "environment_variables" {
  description = "A map of environment variables for the web app"
  type        = map(string)
  default = {
    "ASPNETCORE_ENVIRONMENT"              = "DevNew"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
  }
}

variable "docker_image_name" {
  description = "The name and tag of the Docker image (e.g., 'myapp:latest')"
  type        = string
}

variable "key_vault_id" {
  description = "The ID of the existing Key Vault"
  type        = string
  default     = null
}
