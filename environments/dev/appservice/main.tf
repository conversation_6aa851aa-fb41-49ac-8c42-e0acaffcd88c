locals {
  web_app_name = "${var.environment}-${var.name}-app"
}

data "azurerm_client_config" "current" {}

resource "azurerm_linux_web_app" "app" {
  name                = local.web_app_name
  resource_group_name = var.resource_group_name
  location            = var.location
  service_plan_id     = var.service_plan_id

  https_only                    = true
  client_affinity_enabled       = true
  public_network_access_enabled = true

  site_config {
    always_on           = true
    minimum_tls_version = "1.2"
    http2_enabled       = true

    application_stack {
      docker_image_name   = var.docker_image_name
      docker_registry_url = var.docker_registry_url
    }

    container_registry_use_managed_identity = true
  }

  app_settings = var.environment_variables

  logs {
    detailed_error_messages = false
    failed_request_tracing  = false

    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 35
      }
    }
  }

  identity {
    type         = "SystemAssigned"
    identity_ids = null
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      virtual_network_subnet_id,
      tags,
      site_config[0].application_stack[0].docker_image_name,
      site_config[0].application_stack[0].docker_registry_password,
      site_config[0].application_stack[0].docker_registry_username,
      site_config[0].application_stack[0].docker_registry_url,
      app_settings["APPLICATIONINSIGHTS_CONFIGURATION_CONTENT"],
      sticky_settings,
    ]
  }
}

resource "azurerm_role_assignment" "slot_acr_pull" {
  scope                = "/subscriptions/d40f4d52-4fce-4731-be1a-572bb2cdc92e/resourceGroups/dev-rg-app/providers/Microsoft.ContainerRegistry/registries/gbagroup"
  role_definition_name = "AcrPull"
  principal_id         = azurerm_linux_web_app.app.identity[0].principal_id
}

resource "azurerm_key_vault_access_policy" "app_policy" {
  count        = var.key_vault_id != null ? 1 : 0
  key_vault_id = var.key_vault_id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.app.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}
