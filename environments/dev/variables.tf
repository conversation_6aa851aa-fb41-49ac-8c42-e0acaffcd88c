variable "environment" {
  type        = string
  description = "Environment name"
  default     = "dev"
}

variable "location" {
  type        = string
  description = "Azure region"
  default     = "uksouth"
}

variable "location_short" {
  type        = string
  description = "Short name for Azure region"
  default     = "uks"
}

variable "container_registry_id" {
  type        = string
  description = "ID of the Azure Container Registry from dev environment used for pulling container images"
}
