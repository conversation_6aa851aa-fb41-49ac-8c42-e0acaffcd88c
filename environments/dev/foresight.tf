module "foresight_fe_custom_domain" {
  source = "../../modules/appservice/custom-domain"

  domain_name                   = "dev.foresight-fe.gbatechnologies.net"
  app_service_name              = "dev-foresight-fe"
  resource_group_name           = "dev-foresight-rg"
  cloudflare_zone_id            = "f9919cce5fbe55edc69a584e174499dd"
  custom_domain_verification_id = "02616496297BC58023180D718A53A29B9A77DAA9DA7AF267FFDDF0C15A8B50AF"
  app_service_default_hostname  = "dev-foresight-fe.azurewebsites.net"

  tags = {
    "Accessibility"       = "Internal,On-premise,External,Public"
    "Application"         = "Not-Set"
    "BusinessCriticality" = "Low/Medium/High/Critical"
    "CostCenter"          = "Not-Set"
    "CreatedBy"           = "Unknown"
    "Environment"         = "dev"
    "ExpirationDate"      = "Not-Set-Use-YYYY-MM-DD"
    "Owner"               = "<EMAIL>"
    "Project"             = "Not-Set"
    "Usage"               = "DEV2025/CanBeRemoved/NeedsReview"
  }
}
