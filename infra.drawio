<mxfile host="65bd71144e">
    <diagram id="l5SDMaAGq7Z-UyEwIbzR" name="Azure Infrastructure">
        <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                
                <!-- Azure Subscription -->
                <mxCell id="subscription" value="Azure Subscription" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="40" y="40" width="1020" height="760" as="geometry"/>
                </mxCell>
                
                <!-- Shared Resources -->
                <mxCell id="shared_resources" value="Shared Resources" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="subscription">
                    <mxGeometry x="20" y="40" width="280" height="200" as="geometry"/>
                </mxCell>
                
                <mxCell id="acr" value="Azure Container Registry&#10;gbagroup.azurecr.io" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="shared_resources">
                    <mxGeometry x="20" y="40" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <mxCell id="log_analytics" value="Log Analytics Workspace&#10;(Centralized Monitoring)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="shared_resources">
                    <mxGeometry x="20" y="120" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <!-- Development Environment -->
                <mxCell id="dev_env" value="Development Environment" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="subscription">
                    <mxGeometry x="20" y="260" width="280" height="480" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_rg" value="Resource Group: dev-rg" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="dev_env">
                    <mxGeometry x="20" y="40" width="240" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_asp" value="App Service Plan (Linux)&#10;Standard Tier" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dev_env">
                    <mxGeometry x="20" y="100" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_apps" value="App Services" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="dev_env">
                    <mxGeometry x="20" y="180" width="240" height="180" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_oneview_be" value="dev-oneview-backend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dev_apps">
                    <mxGeometry x="20" y="30" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_oneview_fe" value="dev-oneview-frontend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dev_apps">
                    <mxGeometry x="20" y="80" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_tms_be" value="dev-tms-backend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dev_apps">
                    <mxGeometry x="20" y="130" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_redis" value="Redis Cache&#10;(Basic SKU)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dev_env">
                    <mxGeometry x="20" y="380" width="240" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="dev_sql" value="Azure SQL Server&#10;+ Elastic Pool (Standard)&#10;+ Firewall Rules" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dev_env">
                    <mxGeometry x="20" y="440" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <!-- Staging Environment -->
                <mxCell id="staging_env" value="Staging Environment" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="subscription">
                    <mxGeometry x="320" y="260" width="280" height="480" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_rg" value="Resource Group: staging-rg" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="staging_env">
                    <mxGeometry x="20" y="40" width="240" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_asp" value="App Service Plan (Linux)&#10;Standard Tier" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="staging_env">
                    <mxGeometry x="20" y="100" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_apps" value="App Services" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="staging_env">
                    <mxGeometry x="20" y="180" width="240" height="180" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_oneview_be" value="staging-oneview-backend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="staging_apps">
                    <mxGeometry x="20" y="30" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_oneview_fe" value="staging-oneview-frontend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="staging_apps">
                    <mxGeometry x="20" y="80" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_tms_be" value="staging-tms-backend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="staging_apps">
                    <mxGeometry x="20" y="130" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_redis" value="Redis Cache&#10;(Basic SKU)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="staging_env">
                    <mxGeometry x="20" y="380" width="240" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="staging_sql" value="Azure SQL Server&#10;+ Elastic Pool (Standard)&#10;+ Firewall Rules" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="staging_env">
                    <mxGeometry x="20" y="440" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <!-- Production Environment -->
                <mxCell id="prod_env" value="Production Environment" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="subscription">
                    <mxGeometry x="620" y="260" width="280" height="480" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_rg" value="Resource Group: prod-rg" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="prod_env">
                    <mxGeometry x="20" y="40" width="240" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_asp" value="App Service Plan (Linux)&#10;Premium Tier" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="prod_env">
                    <mxGeometry x="20" y="100" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_apps" value="App Services" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="prod_env">
                    <mxGeometry x="20" y="180" width="240" height="180" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_oneview_be" value="prod-oneview-backend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="prod_apps">
                    <mxGeometry x="20" y="30" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_oneview_fe" value="prod-oneview-frontend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="prod_apps">
                    <mxGeometry x="20" y="80" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_tms_be" value="prod-tms-backend&#10;(Container App + App Insights)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="prod_apps">
                    <mxGeometry x="20" y="130" width="200" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_redis" value="Redis Cache&#10;(Standard SKU)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="prod_env">
                    <mxGeometry x="20" y="380" width="240" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="prod_sql" value="Azure SQL Server&#10;+ Elastic Pool (Premium)&#10;+ Firewall Rules" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="prod_env">
                    <mxGeometry x="20" y="440" width="240" height="60" as="geometry"/>
                </mxCell>
                
                <!-- DNS & Custom Domains -->
                <mxCell id="dns" value="DNS &amp; Custom Domains" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="subscription">
                    <mxGeometry x="320" y="40" width="580" height="200" as="geometry"/>
                </mxCell>
                
                <mxCell id="cloudflare" value="Cloudflare DNS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="dns">
                    <mxGeometry x="20" y="40" width="540" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="domains" value="Custom Domains:&#10;*.oneview.gba4pl.com&#10;*.api.oneview.gba4pl.com&#10;*.api.tms.gba4pl.com" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="dns">
                    <mxGeometry x="20" y="100" width="540" height="80" as="geometry"/>
                </mxCell>
                
                <!-- Key Vaults -->
                <mxCell id="key_vaults" value="Key Vaults (per environment)" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="subscription">
                    <mxGeometry x="920" y="260" width="100" height="480" as="geometry"/>
                </mxCell>
                
                <mxCell id="kv_oneview_be" value="oneview-be" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="key_vaults">
                    <mxGeometry x="10" y="40" width="80" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="kv_oneview_fe" value="oneview-fe" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="key_vaults">
                    <mxGeometry x="10" y="90" width="80" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="kv_tms_be" value="tms-be" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="key_vaults">
                    <mxGeometry x="10" y="140" width="80" height="40" as="geometry"/>
                </mxCell>
                
                <!-- Relationships -->
                <!-- ACR to App Services -->
                <mxCell id="acr_to_dev" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="subscription" source="acr" target="dev_apps">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                
                <mxCell id="acr_to_staging" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="subscription" source="acr" target="staging_apps">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                
                <mxCell id="acr_to_prod" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="subscription" source="acr" target="prod_apps">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                
                <!-- Log Analytics to App Services -->
                <mxCell id="logs_to_apps" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="subscription" source="log_analytics" target="staging_apps">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>