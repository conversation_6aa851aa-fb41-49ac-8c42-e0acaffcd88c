variable "domain_name" {
  description = "The custom domain name to bind to the app service"
  type        = string
}

variable "app_service_name" {
  description = "Name of the App Service to bind custom domains to"
  type        = string
}

variable "resource_group_name" {
  description = "Name of the resource group containing the App Service"
  type        = string
}

variable "cloudflare_zone_id" {
  description = "Cloudflare Zone ID for the custom domain"
  type        = string
}

variable "custom_domain_verification_id" {
  description = "The domain verification ID from the App Service"
  type        = string
}

variable "app_service_default_hostname" {
  description = "The default hostname of the App Service"
  type        = string
}

variable "tags" {
  description = "Tags to apply to the custom domain binding"
  type        = map(string)
  default     = {}
}
