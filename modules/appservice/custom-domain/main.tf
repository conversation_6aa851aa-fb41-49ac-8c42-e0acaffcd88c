terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

# Create domain verification TXT record in Cloudflare
resource "cloudflare_record" "domain_verification" {
  zone_id = var.cloudflare_zone_id
  name    = "asuid.${regex("^(.*)\\.[a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+$", var.domain_name)[0]}"
  type    = "TXT"
  content = "\"${var.custom_domain_verification_id}\""
  proxied = false
  comment = "Managed by Terraform"
}

# Create CNAME record for custom domain in Cloudflare
resource "cloudflare_record" "custom_domain" {
  zone_id = var.cloudflare_zone_id
  name    = var.domain_name
  type    = "CNAME"
  content = var.app_service_default_hostname
  proxied = false
  comment = "Managed by Terraform"
}

resource "time_sleep" "wait_for_dns_propagation" {
  depends_on      = [cloudflare_record.domain_verification, cloudflare_record.custom_domain]
  create_duration = "60s"
}

# Custom hostname binding for the App Service
resource "azurerm_app_service_custom_hostname_binding" "custom_domain" {
  hostname            = var.domain_name
  app_service_name    = var.app_service_name
  resource_group_name = var.resource_group_name

  depends_on = [time_sleep.wait_for_dns_propagation]

  lifecycle {
    ignore_changes = [ssl_state, thumbprint]
  }
}

# Create managed certificate for custom domain
resource "azurerm_app_service_managed_certificate" "custom_domain" {
  custom_hostname_binding_id = azurerm_app_service_custom_hostname_binding.custom_domain.id
  tags                       = var.tags
}

# Bind the managed certificate to the custom domain
resource "azurerm_app_service_certificate_binding" "custom_domain" {
  hostname_binding_id = azurerm_app_service_custom_hostname_binding.custom_domain.id
  certificate_id      = azurerm_app_service_managed_certificate.custom_domain.id
  ssl_state           = "SniEnabled"
}
