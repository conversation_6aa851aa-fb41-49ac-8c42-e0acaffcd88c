terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

data "azurerm_client_config" "current" {}

locals {
  app_name = "${var.environment}-${var.name}"
  # 3-24 character string, containing only 0-9, a-z, A-Z, and not consecutive -.
  key_vault_name = var.use_key_vault ? (var.key_vault_name != null ? "${var.environment}-${var.key_vault_name}" : "${var.environment}-${var.name}-kv") : null
  demo_environment = var.use_key_vault && var.enable_demo_secret ? {
    "KEYVAULT_NAME" = local.key_vault_name
  } : {}
}

# This will create an Application Insights resource for the App Service.
resource "azurerm_application_insights" "insights" {
  name                = "${var.environment}-appi-${var.name}"
  location            = var.location
  resource_group_name = var.resource_group_name
  application_type    = "web"
  retention_in_days   = 90
  workspace_id        = var.log_analytics_workspace_id

  tags = var.tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_linux_web_app" "app" {
  name                = local.app_name
  resource_group_name = var.resource_group_name
  location            = var.location
  service_plan_id     = var.service_plan_id

  https_only                    = true
  client_affinity_enabled       = var.client_affinity_enabled
  public_network_access_enabled = true
  virtual_network_subnet_id     = var.virtual_network_subnet_id

  site_config {
    always_on           = true
    minimum_tls_version = "1.2"
    http2_enabled       = true

    application_stack {
      docker_image_name   = "demos/azure-keyvault-demo:latest"
      docker_registry_url = "https://gbagroup.azurecr.io"
    }

    container_registry_use_managed_identity = true
    health_check_path                       = var.health_check_path
    health_check_eviction_time_in_min       = var.health_check_eviction_time_in_min
    vnet_route_all_enabled                  = var.virtual_network_enabled
  }

  app_settings = merge(var.environment_variables, local.demo_environment, {
    "APPLICATIONINSIGHTS_CONNECTION_STRING"      = azurerm_application_insights.insights.connection_string
    "ApplicationInsightsAgent_EXTENSION_VERSION" = "~3"
  })

  logs {
    detailed_error_messages = false
    failed_request_tracing  = false

    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 35
      }
    }
  }

  # This will work with Azure Container Registry to pull images without any additional configuration.
  identity {
    type         = "SystemAssigned"
    identity_ids = null
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      tags,
      site_config[0].application_stack[0].docker_image_name,
      site_config[0].application_stack[0].docker_registry_password,
      site_config[0].application_stack[0].docker_registry_username,
      site_config[0].application_stack[0].docker_registry_url,
      app_settings["APPLICATIONINSIGHTS_CONFIGURATION_CONTENT"],
      sticky_settings,
    ]
  }
}

# Create deployment slots
resource "azurerm_linux_web_app_slot" "slots" {
  for_each = toset(var.deployment_slots)

  name           = each.key
  app_service_id = azurerm_linux_web_app.app.id

  https_only                    = true
  client_affinity_enabled       = var.client_affinity_enabled
  public_network_access_enabled = true

  site_config {
    always_on           = true
    minimum_tls_version = "1.2"
    http2_enabled       = true

    application_stack {
      docker_image_name   = "demos/azure-keyvault-demo:latest"
      docker_registry_url = "https://gbagroup.azurecr.io"
    }

    container_registry_use_managed_identity = true
    health_check_path                       = var.health_check_path
    health_check_eviction_time_in_min       = var.health_check_eviction_time_in_min
    vnet_route_all_enabled                  = var.virtual_network_enabled
  }

  app_settings = merge(var.environment_variables, local.demo_environment)

  logs {
    detailed_error_messages = false
    failed_request_tracing  = false

    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 35
      }
    }
  }

  # This will work with Azure Container Registry to pull images without any additional configuration.
  identity {
    type         = "SystemAssigned"
    identity_ids = null
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      virtual_network_subnet_id,
      tags,
      site_config[0].application_stack[0].docker_image_name,
      site_config[0].application_stack[0].docker_registry_password,
      site_config[0].application_stack[0].docker_registry_username,
      site_config[0].application_stack[0].docker_registry_url,
      app_settings["APPLICATIONINSIGHTS_CONFIGURATION_CONTENT"]
    ]
  }
}

# Add ACR pull role for slots
resource "azurerm_role_assignment" "slot_acr_pull" {
  for_each = toset(var.deployment_slots)

  scope                = var.container_registry_id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_linux_web_app_slot.slots[each.key].identity[0].principal_id
}

# Add Key Vault access policy for slots
resource "azurerm_key_vault_access_policy" "slot_policy" {
  for_each = var.use_key_vault ? toset(var.deployment_slots) : []

  key_vault_id = azurerm_key_vault.app_vault[0].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app_slot.slots[each.key].identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}

resource "azurerm_key_vault" "app_vault" {
  count                       = var.use_key_vault ? 1 : 0
  name                        = local.key_vault_name
  location                    = var.location
  resource_group_name         = var.resource_group_name
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  sku_name                    = "standard"
  enabled_for_disk_encryption = true
  soft_delete_retention_days  = 7
  purge_protection_enabled    = true
  enable_rbac_authorization   = false

  network_acls {
    default_action             = var.key_vault_network_acls_default_action
    bypass                     = var.key_vault_network_acls_bypass
    ip_rules                   = var.key_vault_network_acls_ip_rules
    virtual_network_subnet_ids = var.key_vault_network_acls_virtual_network_subnet_ids
  }

  tags = merge(var.tags, {
    KeyVaultFor = local.app_name
  })

  lifecycle {
    ignore_changes = [
      tags["CreatedAt"],
    ]
  }
}

data "azurerm_monitor_diagnostic_categories" "app_service" {
  resource_id = azurerm_linux_web_app.app.id
}

resource "azurerm_monitor_diagnostic_setting" "app_service" {
  name                       = "${local.app_name}-diagnostics"
  target_resource_id         = azurerm_linux_web_app.app.id
  log_analytics_workspace_id = var.log_analytics_workspace_id

  dynamic "enabled_log" {
    for_each = data.azurerm_monitor_diagnostic_categories.app_service.log_category_types
    content {
      category = enabled_log.value
    }
  }

  metric {
    category = "AllMetrics"
    enabled  = true
  }
}

# Add diagnostic settings for the Key Vault
resource "azurerm_monitor_diagnostic_setting" "key_vault" {
  count                      = var.use_key_vault ? 1 : 0
  name                       = "${local.key_vault_name}-diagnostics"
  target_resource_id         = azurerm_key_vault.app_vault[0].id
  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category = "AuditEvent"
  }

  enabled_log {
    category = "AzurePolicyEvaluationDetails"
  }

  metric {
    category = "AllMetrics"
    enabled  = true
  }
}

# Add access policy for the App Service
resource "azurerm_key_vault_access_policy" "app_policy" {
  count        = var.use_key_vault ? 1 : 0
  key_vault_id = azurerm_key_vault.app_vault[0].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.app.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}

# Add access policy for the Service Principal (Terraform)
resource "azurerm_key_vault_access_policy" "terraform_policy" {
  count        = var.use_key_vault ? 1 : 0
  key_vault_id = azurerm_key_vault.app_vault[0].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_client_config.current.object_id

  secret_permissions = [
    "Backup",
    "Delete",
    "Get",
    "List",
    "Recover",
    "Restore",
    "Set"
  ]
}


# Create a test secret in the Key Vault
resource "azurerm_key_vault_secret" "test_secret" {
  count        = var.use_key_vault && var.enable_demo_secret ? 1 : 0
  name         = "TestSecret"
  value        = "This is a test secret value created at ${timestamp()}"
  key_vault_id = azurerm_key_vault.app_vault[0].id
  depends_on   = [azurerm_key_vault_access_policy.terraform_policy]

  lifecycle {
    ignore_changes = [
      value,
      tags
    ]
  }
}

# This will allow the App Service to pull images from the Container Registry.
resource "azurerm_role_assignment" "acr_pull" {
  scope                = var.container_registry_id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_linux_web_app.app.identity[0].principal_id
}

# Custom hostname bindings for the App Service
resource "azurerm_app_service_custom_hostname_binding" "custom_domains" {
  for_each            = toset(var.custom_domains)
  hostname            = each.value
  app_service_name    = azurerm_linux_web_app.app.name
  resource_group_name = var.resource_group_name

  depends_on = [cloudflare_record.domain_verification]

  lifecycle {
    ignore_changes = [ssl_state, thumbprint]
  }
}

# Create domain verification TXT records in Cloudflare
resource "cloudflare_record" "domain_verification" {
  for_each = var.cloudflare_zone_id != null ? toset(var.custom_domains) : toset([])
  zone_id  = var.cloudflare_zone_id
  name     = "asuid.${regex("^(.*)\\.[a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+$", each.value)[0]}"
  type     = "TXT"
  content  = "\"${azurerm_linux_web_app.app.custom_domain_verification_id}\""
  proxied  = false
  comment  = "Managed by Terraform"
}

resource "time_sleep" "wait_for_dns_propagation" {
  depends_on      = [cloudflare_record.domain_verification]
  create_duration = "60s"
}

# Create CNAME records for custom domains in Cloudflare
resource "cloudflare_record" "custom_domains" {
  for_each = var.cloudflare_zone_id != null ? toset(var.custom_domains) : toset([])
  zone_id  = var.cloudflare_zone_id
  name     = each.value
  type     = "CNAME"
  content  = azurerm_linux_web_app.app.default_hostname
  proxied  = false
  comment  = "Managed by Terraform"
}

# Create managed certificates for custom domains
resource "azurerm_app_service_managed_certificate" "custom_domains" {
  for_each                   = toset(var.custom_domains)
  custom_hostname_binding_id = azurerm_app_service_custom_hostname_binding.custom_domains[each.key].id
  depends_on                 = [time_sleep.wait_for_dns_propagation]

  tags = merge(var.tags, {
    KeyVaultFor = local.app_name
  })

  lifecycle {
    ignore_changes = [
      tags # https://github.com/hashicorp/terraform-provider-azurerm/issues/25942
    ]
  }
}

# Bind the managed certificates to the custom domains
resource "azurerm_app_service_certificate_binding" "custom_domains" {
  for_each            = toset(var.custom_domains)
  hostname_binding_id = azurerm_app_service_custom_hostname_binding.custom_domains[each.key].id
  certificate_id      = azurerm_app_service_managed_certificate.custom_domains[each.key].id
  ssl_state           = "SniEnabled"

  depends_on = [time_sleep.wait_for_dns_propagation]
}
