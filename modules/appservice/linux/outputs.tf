output "id" {
  description = "The ID of the Linux Web App"
  value       = azurerm_linux_web_app.app.id
}

output "default_hostname" {
  description = "The default hostname of the Linux Web App"
  value       = azurerm_linux_web_app.app.default_hostname
}

output "identity" {
  description = "The identity block of the Linux Web App"
  value       = azurerm_linux_web_app.app.identity
}

output "custom_domain_verification_id" {
  description = "The custom domain verification ID"
  value       = azurerm_linux_web_app.app.custom_domain_verification_id
}

output "outbound_ip_addresses" {
  description = "A comma separated list of outbound IP addresses"
  value       = azurerm_linux_web_app.app.outbound_ip_addresses
}

output "key_vault_id" {
  description = "The ID of the Key Vault"
  value       = var.use_key_vault ? azurerm_key_vault.app_vault[0].id : null
}

output "key_vault_uri" {
  description = "The URI of the Key Vault"
  value       = var.use_key_vault ? azurerm_key_vault.app_vault[0].vault_uri : null
}
