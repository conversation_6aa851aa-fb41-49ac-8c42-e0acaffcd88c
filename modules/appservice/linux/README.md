# Linux App Service Module

This module creates an Azure Linux App Service with optional Key Vault integration and custom domain support.

## Required Parameters

| Parameter                  | Type   | Description                                                                                 |
| -------------------------- | ------ | ------------------------------------------------------------------------------------------- |
| name                       | string | The name of the Linux Web App. Must be lowercase and can include hyphens. Example: "gba-ui" |
| environment                | string | The environment name (e.g. dev, test, prod)                                                 |
| resource_group_name        | string | The name of the resource group                                                              |
| location                   | string | The Azure region where resources will be created                                            |
| service_plan_id            | string | The ID of the App Service Plan                                                              |
| log_analytics_workspace_id | string | The ID of the Log Analytics Workspace                                                       |

## Optional Parameters

| Parameter                         | Type         | Default | Description                                                                      |
| --------------------------------- | ------------ | ------- | -------------------------------------------------------------------------------- |
| container_registry_id             | string       | null    | The ID of the Container Registry to pull images from                             |
| use_key_vault                     | bool         | false   | Whether to use Key Vault integration                                             |
| enable_demo_secret                | bool         | false   | Whether to enable the demo secret                                                |
| key_vault_name                    | string       | null    | Optional name for the Key Vault. Environment name will be prefixed automatically |
| environment_variables             | map(string)  | {}      | Map of environment variables                                                     |
| health_check_path                 | string       | null    | Path to the health check endpoint                                                |
| health_check_eviction_time_in_min | number       | null    | Minutes before unhealthy node removal                                            |
| custom_domains                    | list(string) | []      | List of custom domain names to bind                                              |
| cloudflare_zone_id                | string       | null    | Cloudflare Zone ID for domain management                                         |
| deployment_slots                  | list(string) | []      | List of deployment slot names to create                                          |
| tags                              | map(string)  | {}      | Map of tags to assign to resources                                               |

## Note

Since resource groups only store metadata about resources, it is not necessary for resources in a resource group to be located in the same region as the resource group itself. That's why we need to specify the location despite the fact that the resource group name is provided.