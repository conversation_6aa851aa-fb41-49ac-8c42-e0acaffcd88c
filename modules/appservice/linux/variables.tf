variable "name" {
  description = "The name of the Linux Web App, no prefix required. Lowercase. e.g. gba-ui"
  validation {
    condition     = can(regex("^[a-z0-9]+(-[a-z0-9]+)*$", var.name))
    error_message = "The name must be lowercase and can include hyphens."
  }
  type = string
}

variable "environment" {
  description = "The environment name, e.g. dev, test, prod"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The Azure region where resources will be created"
  type        = string
}

variable "service_plan_id" {
  description = "The ID of the App Service Plan"
  type        = string
}

variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
  default     = {}
}

variable "log_analytics_workspace_id" {
  description = "The ID of the Log Analytics Workspace"
  type        = string
}

variable "container_registry_id" {
  description = "The ID of the Container Registry to pull images from"
  type        = string
  default     = null
}

variable "use_key_vault" {
  description = "Whether to use the Key Vault"
  type        = bool
  default     = false
}

variable "enable_demo_secret" {
  description = "Whether to enable the demo secret"
  type        = bool
  default     = false
}

variable "key_vault_name" {
  description = "Optional name for the Key Vault. If not provided, a random string will be generated. Do NOT include the environment name in the name."
  type        = string
  default     = null
}

variable "environment_variables" {
  description = "A map of environment variables"
  type        = map(string)
  default     = {}
}

variable "health_check_path" {
  description = "Path to the health check endpoint"
  type        = string
  default     = null
}

variable "health_check_eviction_time_in_min" {
  description = "The amount of time in minutes that a node can be unhealthy before being removed from the load balancer"
  type        = number
  default     = null
}

variable "custom_domains" {
  description = "List of custom domain names to bind to the App Service"
  type        = list(string)
  default     = []
}

variable "cloudflare_zone_id" {
  description = "The Zone ID from Cloudflare for the domain (optional)"
  type        = string
  default     = null
}

variable "deployment_slots" {
  description = "List of deployment slot names to create"
  type        = list(string)
  default     = []
}

variable "virtual_network_enabled" {
  description = "Whether to enable the Virtual Network"
  type        = bool
  default     = false
}

variable "virtual_network_subnet_id" {
  description = "The ID of the Virtual Network Subnet to use"
  type        = string
  default     = null
}

variable "key_vault_network_acls_default_action" {
  description = "The default action to use when no rule matches (Allow or Deny)"
  type        = string
  default     = "Allow"
  validation {
    condition     = contains(["Allow", "Deny"], var.key_vault_network_acls_default_action)
    error_message = "The default_action must be either 'Allow' or 'Deny'."
  }
}

variable "key_vault_network_acls_bypass" {
  description = "Which traffic can bypass network rules (AzureServices, None)"
  type        = string
  default     = "AzureServices"
  validation {
    condition     = contains(["AzureServices", "None"], var.key_vault_network_acls_bypass)
    error_message = "The bypass must be either 'AzureServices' or 'None'."
  }
}

variable "key_vault_network_acls_ip_rules" {
  description = "List of IP addresses or CIDR blocks that are allowed to access the Key Vault"
  type        = list(string)
  default     = []
}

variable "key_vault_network_acls_virtual_network_subnet_ids" {
  description = "List of virtual network subnet IDs that are allowed to access the Key Vault"
  type        = list(string)
  default     = []
}

variable "client_affinity_enabled" {
  description = "value of client affinity enabled"
  type        = bool
  default     = true
}