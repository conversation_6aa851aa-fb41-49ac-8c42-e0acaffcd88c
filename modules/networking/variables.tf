variable "environment" {
  description = "The name of the environment"
  type        = string
}

variable "location_short" {
  description = "The shortened location name"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "address_space" {
  description = "The address space for the virtual network"
  type        = list(string)
}

variable "subnets" {
  type = list(object({
    name              = string
    address_prefix    = string
    service_endpoints = optional(list(string))
    delegation = optional(object({
      name = string
      service_delegation = object({
        name    = string
        actions = list(string)
      })
    }))
  }))
  description = "List of subnets to create. Each subnet can optionally specify service endpoints and delegation."
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
