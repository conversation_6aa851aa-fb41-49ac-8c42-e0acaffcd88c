# Azure Container App Environment Module

This module creates the core infrastructure for Azure Container Apps but does not create the container apps themselves.

## Resources Created

- Container App Environment
- User Assigned Identity
- Key Vault
- Role Assignments for ACR access
- Key Vault Access Policies

## Usage

```hcl
module "container_app_infra" {
  source = "../../modules/container-app-environment"
  
  name                       = "${var.environment}-containerapp"
  resource_group_name        = azurerm_resource_group.app.name
  location                   = var.location
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  infrastructure_subnet_id   = var.infrastructure_subnet_id
  container_registry_id      = var.container_registry_id
  tags                       = local.common_tags
}

# Then create container apps separately using the module outputs
resource "azurerm_container_app" "app" {
  name                         = "my-app"
  container_app_environment_id = module.container_app_infra.environment_id
  resource_group_name          = azurerm_resource_group.app.name
  revision_mode                = "Single"
  
  identity {
    type = "UserAssigned"
    identity_ids = [module.container_app_infra.identity_id]
  }
  
  # Other container app configurations...
  
  registry {
    server   = "myregistry.azurecr.io"
    identity = module.container_app_infra.identity_id
  }
}
```

## Requirements

- Subnet must be delegated to Microsoft.App/environments
- Container Registry must exist
- Log Analytics Workspace must exist

## Inputs

| Name                                          | Description                                                                                                                                                                 | Type         | Default      | Required |
| --------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------ | ------------ | -------- |
| name                                          | Name for the Container App Environment and related resources                                                                                                                | string       | -            | yes      |
| resource_group_name                           | Name of the resource group                                                                                                                                                  | string       | -            | yes      |
| location                                      | Azure region for resources                                                                                                                                                  | string       | -            | yes      |
| log_analytics_workspace_id                    | ID of the Log Analytics workspace                                                                                                                                           | string       | -            | yes      |
| infrastructure_subnet_id                      | ID of the subnet dedicated to Container Apps                                                                                                                                | string       | -            | yes      |
| internal_load_balancer_enabled                | Whether to enable internal load balancer                                                                                                                                    | bool         | true         | no       |
| container_registry_id                         | ID of the Azure Container Registry                                                                                                                                          | string       | -            | yes      |
| workload_profile_enabled                      | Whether to enable workload profiles                                                                                                                                         | bool         | true         | no       |
| workload_profile_name                         | Name of the workload profile                                                                                                                                                | string       | "production" | no       |
| workload_profile_type                         | Type of workload profile                                                                                                                                                    | string       | "D4"         | no       |
| workload_profile_min_count                    | Minimum count of workload profile nodes                                                                                                                                     | number       | 1            | no       |
| workload_profile_max_count                    | Maximum count of workload profile nodes                                                                                                                                     | number       | 3            | no       |
| key_vault_sku                                 | SKU of the Key Vault                                                                                                                                                        | string       | "standard"   | no       |
| tags                                          | Tags to apply to all resources                                                                                                                                              | map(string)  | {}           | no       |
| enable_private_endpoint                       | Enable private endpoint for the Container App Environment.                                                                                                                  | bool         | false        | no       |
| private_dns_zone_id                           | The resource ID of the existing Private DNS Zone to link. Required if enable_private_endpoint is true.                                                                      | string       | null         | no       |
| private_endpoint_subnet_id                    | The resource ID of the subnet for the Private DNS Zone virtual network link and Container App Environment\'s private endpoint. Required if enable_private_endpoint is true. | string       | null         | no       |
| container_app_environment_pe_name             | Name for the Private Endpoint of the Container App Environment. Required if enable_private_endpoint is true.                                                                | string       | null         | no       |
| private_endpoint_subnet_id_for_environment_pe | The resource ID of the subnet for the Container App Environment\'s private endpoint. Required if enable_private_endpoint is true.                                           | string       | null         | no       |
| private_dns_zone_id_for_environment_pe        | The resource ID of the Private DNS Zone to link with the Container App Environment\'s private endpoint. Required if enable_private_endpoint is true.                        | string       | null         | no       |
| endpoint_virtual_network_id                   | The resource ID of the virtual network to link with the Private DNS Zone.                                                                                                   | string       | null         | no       |
| container_apps                                | A list of container apps to create in the environment. See variables.tf for object structure.                                                                               | list(object) | []           | no       |

## Outputs

| Name                          | Description                                          |
| ----------------------------- | ---------------------------------------------------- |
| environment_id                | ID of the Container App Environment                  |
| environment_default_domain    | Default domain name of the Container App Environment |
| environment_static_ip_address | Static IP address of the Container App Environment   |
| identity_id                   | ID of the User Assigned Identity                     |
| identity_principal_id         | Principal ID of the User Assigned Identity           |
| identity_client_id            | Client ID of the User Assigned Identity              |
| key_vault_ids                 | IDs of the Key Vaults                                |
| key_vault_names               | Names of the Key Vaults                              |
| key_vault_uris                | URIs of the Key Vaults                               |

## Notes

- This module doesn't create the container apps themselves, only the supporting infrastructure
- Multiple container apps can be created using the outputs from this module
- The identity created by this module is granted AcrPull permissions to the specified container registry

## Private Network Configuration

When deploying Container App Environment in internal networks, you should configure a private endpoint to ensure secure private access. This is essential for environments where public internet exposure must be minimized.

### Container App Environment Private Endpoint Setup

To configure a private endpoint for the Container App Environment:

```hcl
# 1. Create a Private DNS Zone for Container Apps
resource "azurerm_private_dns_zone" "container_app_dns" {
  name                = "privatelink.azurecontainerapps.io"
  resource_group_name = var.resource_group_name
}

# 2. Link the Private DNS Zone to your VNet
resource "azurerm_private_dns_zone_virtual_network_link" "container_app_dns_link" {
  name                  = "containerapp-dns-link"
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.container_app_dns.name
  virtual_network_id    = var.virtual_network_id
  registration_enabled  = false
}

# 3. Create the Private Endpoint for the Container App Environment
resource "azurerm_private_endpoint" "container_app_pe" {
  name                = "${var.name}-env-pe"
  location            = var.location
  resource_group_name = var.resource_group_name
  subnet_id           = var.private_endpoints_subnet_id  # Dedicated subnet for private endpoints

  private_service_connection {
    name                           = "${var.name}-env-connection"
    is_manual_connection           = false
    private_connection_resource_id = module.container_app_infra.environment_id
    subresource_names              = ["environment"] # The subresource for Container App Environment
  }

  private_dns_zone_group {
    name                 = "containerapp-dns-zone-group"
    private_dns_zone_ids = [azurerm_private_dns_zone.container_app_dns.id]
  }

  tags = var.tags
}
```

### Prerequisites for Private Endpoint Configuration

- A subnet dedicated to private endpoints (different from the Container App infrastructure subnet)
- Virtual Network with proper DNS configuration
- Private DNS zone: `privatelink.azurecontainerapps.io` for Container App Environment
- Network connectivity between your client networks and the VNet containing the private endpoint

### Example usage

# module "container_app_test" {
#   source = "../../modules/container-app-environment"

#   name                       = "container-app-test"
#   resource_group_name        = azurerm_resource_group.app.name
#   location                   = azurerm_resource_group.app.location
#   infrastructure_subnet_id   = "/subscriptions/3ddaac09-f2fc-4b7e-92a5-e6680cd0f839/resourceGroups/prod-meraki-mrg/providers/Microsoft.Network/virtualNetworks/prod-merakivmx-vnet/subnets/test"
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
#   container_registry_id      = var.container_registry_id
# }

# # Define a container app in the environment
# resource "azurerm_container_app" "app" {
#   name                         = "test-app"
#   container_app_environment_id = module.container_app_test.environment_id
#   resource_group_name          = azurerm_resource_group.app.name
#   revision_mode                = "Single"

#   identity {
#     type = "UserAssigned"
#     identity_ids = [
#       module.container_app_test.identity_id,
#     ]
#   }

#   ingress {
#     allow_insecure_connections = true
#     external_enabled           = true

#     target_port = 80
#     transport   = "auto"

#     traffic_weight {
#       percentage      = 100
#       latest_revision = true
#     }
#   }

#   template {
#     container {
#       name   = "nginx-demo"
#       image  = "gbagroup.azurecr.io/nginx:1.28.0-alpine"
#       cpu    = 0.5
#       memory = "1Gi"
#     }
#     min_replicas = 1
#   }

#   registry {
#     server   = "gbagroup.azurecr.io"
#     identity = module.container_app_test.identity_id
#   }

#   tags = merge(local.common_tags, {
#     "Accessibility"       = "Internal,On-premise,External,Public"
#     "Application"         = "Not-Set"
#     "BusinessCriticality" = "Low/Medium/High/Critical"
#     "BusinessUnit"        = "Not-Set"
#     "CostCenter"          = "Not-Set"
#     "CreatedAt"           = "2025-05-21T07:01:48Z"
#     "CreatedBy"           = "Terraform"
#     "CreationDate"        = "2025-05-21"
#     "DataSensitivity"     = "General"
#     "Department"          = "Not-Set"
#     "Environment"         = "prod"
#     "ExpirationDate"      = "Not-Set-Use-YYYY-MM-DD"
#     "OwnedBy"             = "<EMAIL>"
#     "Owner"               = "<EMAIL>"
#     "Project"             = "Not-Set"
#     "Sites"               = "Global"
#     "Usage"               = "PROD2025/CanBeRemoved/NeedsReview"
#   })
# }
