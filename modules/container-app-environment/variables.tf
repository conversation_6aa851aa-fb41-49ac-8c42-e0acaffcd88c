variable "name" {
  type        = string
  description = "Name for the Container App Environment and related resources"
}

variable "resource_group_name" {
  type        = string
  description = "Name of the resource group where resources will be created"
}

variable "location" {
  type        = string
  description = "Azure region where resources will be created"
}

variable "log_analytics_workspace_id" {
  type        = string
  description = "ID of the Log Analytics workspace for Container App Environment monitoring"
}

variable "infrastructure_subnet_id" {
  type        = string
  description = "ID of the subnet dedicated to the Container App Environment infrastructure"
}

variable "internal_load_balancer_enabled" {
  type        = bool
  description = "Whether to enable internal load balancer for the Container App Environment"
  default     = true
}

variable "container_registry_id" {
  type        = string
  description = "ID of the Azure Container Registry to pull images from"
}

variable "workload_profile_enabled" {
  type        = bool
  description = "Whether to enable workload profiles for the Container App Environment"
  default     = true
}

variable "workload_profile_name" {
  type        = string
  description = "Name of the workload profile"
  default     = "production"
}

variable "workload_profile_type" {
  type        = string
  description = "Type of workload profile to use (e.g. D4, E4, etc.)"
  default     = "D4"
}

variable "workload_profile_min_count" {
  type        = number
  description = "Minimum count of workload profile nodes"
  default     = 1
}

variable "workload_profile_max_count" {
  type        = number
  description = "Maximum count of workload profile nodes"
  default     = 3
}

variable "key_vault_sku" {
  type        = string
  description = "SKU of the Key Vaults"
  default     = "standard"
}

# variable "key_vault_names" {
#   type        = list(string)
#   description = "A list of names for the Key Vaults to be created"
#   default     = []
# }

variable "tags" {
  description = "A map of tags to assign to the resources."
  type        = map(string)
  default     = {}
}

variable "enable_private_endpoint" {
  description = "Enable private endpoint for the Container App Environment. If true, the environment will be linked to an existing Private DNS Zone specified by private_dns_zone_id."
  type        = bool
  default     = false
}

variable "private_dns_zone_id" {
  description = "The resource ID of the existing Private DNS Zone to link (e.g., /subscriptions/.../resourceGroups/.../providers/Microsoft.Network/privateDnsZones/privatelink.uksouth.azurecontainerapps.io). Required if enable_private_endpoint is true."
  type        = string
  default     = null
}

variable "private_endpoint_subnet_id" {
  description = "The resource ID of the subnet to be used for the Private DNS Zone virtual network link. Also used for the Container App Environment's private endpoint if enable_private_endpoint is true."
  type        = string
  default     = null
}

variable "container_app_environment_pe_name" {
  description = "Name for the Private Endpoint of the Container App Environment. Required if enable_private_endpoint is true."
  type        = string
  default     = null
}

variable "private_endpoint_subnet_id_for_environment_pe" {
  description = "The resource ID of the subnet to be used for the Container App Environment's private endpoint. Required if enable_private_endpoint is true. Can be the same as private_endpoint_subnet_id."
  type        = string
  default     = null
}

variable "private_dns_zone_id_for_environment_pe" {
  description = "The resource ID of the Private DNS Zone to link with the Container App Environment's private endpoint. Required if enable_private_endpoint is true."
  type        = string
  default     = null
}

variable "endpoint_virtual_network_id" {
  description = "The resource ID of the virtual network to link with the Private DNS Zone."
  type        = string
  default     = null
}

variable "container_apps" {
  type = list(object({
    name          = string
    revision_mode = optional(string, "Single")
    ingress = optional(object({
      allow_insecure_connections = optional(bool, false)
      external_enabled           = optional(bool, false)
      target_port                = number
      transport                  = optional(string, "auto")
      traffic_weight = optional(list(object({
        percentage      = number
        latest_revision = optional(bool, true)
        revision_suffix = optional(string)
        label           = optional(string)
      })), [{ percentage = 100, latest_revision = true }])
    }))
    template = object({
      containers = list(object({
        name   = string
        image  = optional(string, "nginx")
        cpu    = number
        memory = string
        env = optional(list(object({
          name        = string
          value       = optional(string)
          secret_name = optional(string)
        })), [])
        probes = optional(list(object({
          type = string # liveness, readiness, startup
          http_get = optional(object({
            path   = string
            port   = number
            scheme = optional(string, "HTTP")
            http_header = optional(list(object({
              name  = string
              value = string
            })), [])
          }))
          initial_delay_seconds = optional(number)
          period_seconds        = optional(number)
          timeout_seconds       = optional(number)
          failure_threshold     = optional(number)
          success_threshold     = optional(number)
        })), [])
      }))
      min_replicas = optional(number, 0)
      max_replicas = optional(number, 1)
      scale_rules = optional(list(object({
        name = string
        azure_queue = optional(object({
          queue_name   = string
          queue_length = number
          auth = list(object({
            secret_ref        = string
            trigger_parameter = string
          }))
        }))
        http = optional(object({
          metadata = map(string)
          auth = list(object({
            secret_ref        = string
            trigger_parameter = string
          }))
        }))
        custom = optional(object({
          type     = string
          metadata = map(string)
          auth = list(object({
            secret_ref        = string
            trigger_parameter = string
          }))
        }))
      })), [])
    })
    registry = optional(object({
      server   = string
      identity = optional(string) # UserAssignedIdentity ID for ACR pull
    }))
    dapr = optional(object({
      enabled            = bool
      app_id             = string
      app_port           = optional(number)
      app_protocol       = optional(string, "http")
      enable_api_logging = optional(bool)
    }))
    secrets = optional(list(object({
      name  = string
      value = string
    })), [])
    tags           = optional(map(string), {})
    key_vault_name = optional(string) # Name of the Key Vault to associate with this app
  }))
  description = "(Deprecated) A list of container apps to create in the environment"
  default     = []
}

# variable "key_vault_secret_permissions" {
#   type        = list(string)
#   description = "List of secret permissions for the Key Vault access policy"
#   default     = ["Get", "List"]
# }
