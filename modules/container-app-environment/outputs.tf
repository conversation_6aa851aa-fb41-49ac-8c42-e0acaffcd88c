output "environment_id" {
  description = "ID of the Container App Environment"
  value       = azurerm_container_app_environment.container_app_env.id
}

output "environment_default_domain" {
  description = "The default domain name of the Container App Environment"
  value       = azurerm_container_app_environment.container_app_env.default_domain
}

output "environment_static_ip_address" {
  description = "Static IP address of the Container App Environment"
  value       = azurerm_container_app_environment.container_app_env.static_ip_address
}

output "identity_id" {
  description = "ID of the User Assigned Identity for Container Apps"
  value       = azurerm_user_assigned_identity.container_app_identity.id
}

output "identity_principal_id" {
  description = "Principal ID of the User Assigned Identity"
  value       = azurerm_user_assigned_identity.container_app_identity.principal_id
}

output "identity_client_id" {
  description = "Client ID of the User Assigned Identity"
  value       = azurerm_user_assigned_identity.container_app_identity.client_id
}

output "key_vault_ids" {
  description = "IDs of the Key Vaults"
  value       = [for kv in azurerm_key_vault.container_app_kv : kv.id]
}

output "key_vault_names" {
  description = "Names of the Key Vaults"
  value       = [for kv in azurerm_key_vault.container_app_kv : kv.name]
}

output "key_vault_uris" {
  description = "URIs of the Key Vaults"
  value       = [for kv in azurerm_key_vault.container_app_kv : kv.vault_uri]
}
