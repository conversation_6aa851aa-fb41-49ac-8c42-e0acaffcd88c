/*
This module creates the infrastructure for Azure Container Apps, including:
- Container App Environment
- User Assigned Identity
- Key Vault
- Role Assignments and Access Policies

Container Apps themselves should be created separately using the outputs from this module.
*/

# Get current subscription and tenant details
data "azurerm_client_config" "current" {}

# Stage 1: Create the Container App Environment and User Assigned Identity bind it ...
# ... to the Container Registry for pull access

# 1 - User Assigned Identity for Container Apps
resource "azurerm_user_assigned_identity" "container_app_identity" {
  name                = "${var.name}-identity"
  resource_group_name = var.resource_group_name
  location            = var.location
  tags                = var.tags
}

# 2 - Role assignment for ACR pull access
resource "azurerm_role_assignment" "acr_pull" {
  scope                = var.container_registry_id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_user_assigned_identity.container_app_identity.principal_id
}

# 3 - Container App Environment
resource "azurerm_container_app_environment" "container_app_env" {
  name                           = "${var.name}-env"
  location                       = var.location
  resource_group_name            = var.resource_group_name
  log_analytics_workspace_id     = var.log_analytics_workspace_id
  infrastructure_subnet_id       = var.infrastructure_subnet_id
  internal_load_balancer_enabled = var.internal_load_balancer_enabled

  dynamic "workload_profile" {
    for_each = var.workload_profile_enabled ? [1] : []
    content {
      name                  = var.workload_profile_name
      workload_profile_type = var.workload_profile_type
      minimum_count         = var.workload_profile_min_count
      maximum_count         = var.workload_profile_max_count
    }
  }

  tags = var.tags
}

# Stage Network: Configure private endpoint and DNS for the Container App Environment

# Private DNS Zone integration for Container App Environment
resource "azurerm_private_dns_zone_virtual_network_link" "container_app_env_dns_link" {
  count                 = var.enable_private_endpoint && var.private_dns_zone_id != null ? 1 : 0
  name                  = "${var.name}-env-dns-link"
  resource_group_name   = var.enable_private_endpoint && var.private_dns_zone_id != null ? split("/", var.private_dns_zone_id)[4] : null
  private_dns_zone_name = var.enable_private_endpoint && var.private_dns_zone_id != null ? split("/", var.private_dns_zone_id)[8] : null
  virtual_network_id    = var.endpoint_virtual_network_id
  registration_enabled  = false # Typically false for this scenario

  lifecycle {
    precondition {
      condition     = !(var.enable_private_endpoint && var.endpoint_virtual_network_id == null)
      error_message = "endpoint_virtual_network_id must be provided when enable_private_endpoint is true."
    }
    precondition {
      condition     = var.private_dns_zone_id != null
      error_message = "private_dns_zone_id (ID of the existing Private DNS Zone) must be provided when enable_private_endpoint is true."
    }
  }

  depends_on = [
    azurerm_container_app_environment.container_app_env,
  ]

  tags = var.tags
}

# Private Endpoint for Container App Environment
resource "azurerm_private_endpoint" "container_app_env_pe" {
  count               = var.enable_private_endpoint ? 1 : 0 # Changed to use var.enable_private_endpoint
  name                = var.container_app_environment_pe_name
  location            = var.location
  resource_group_name = var.resource_group_name
  subnet_id           = var.private_endpoint_subnet_id_for_environment_pe # Ensure this is the correct subnet for the PE itself

  private_service_connection {
    name                           = "${var.container_app_environment_pe_name}-psc"
    private_connection_resource_id = azurerm_container_app_environment.container_app_env.id
    is_manual_connection           = false
    subresource_names              = ["managedEnvironments"]
  }

  private_dns_zone_group {
    name                 = "default"
    private_dns_zone_ids = [var.private_dns_zone_id_for_environment_pe]
  }

  tags = var.tags

  lifecycle {
    precondition {
      condition = !var.enable_private_endpoint || (
        var.enable_private_endpoint &&
        var.container_app_environment_pe_name != null &&
        var.private_endpoint_subnet_id_for_environment_pe != null &&
        var.private_dns_zone_id_for_environment_pe != null
      )
      error_message = "When enable_private_endpoint is true, container_app_environment_pe_name, private_endpoint_subnet_id_for_environment_pe, and private_dns_zone_id_for_environment_pe must be provided for the environment's private endpoint."
    }
  }

  depends_on = [
    azurerm_container_app_environment.container_app_env,
  ]
}

# Stage 2: Create Container Apps and their Key Vaults with access policies

# 4 - Container Apps
resource "azurerm_container_app" "apps" {
  for_each = { for app in var.container_apps : app.name => app }

  name                         = each.value.name
  container_app_environment_id = azurerm_container_app_environment.container_app_env.id
  resource_group_name          = var.resource_group_name
  revision_mode                = each.value.revision_mode
  workload_profile_name        = "Consumption"

  identity {
    type         = "SystemAssigned, UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app_identity.id]
  }

  dynamic "ingress" {
    for_each = each.value.ingress != null ? [each.value.ingress] : []
    content {
      allow_insecure_connections = ingress.value.allow_insecure_connections
      external_enabled           = ingress.value.external_enabled
      target_port                = ingress.value.target_port
      transport                  = ingress.value.transport
      dynamic "traffic_weight" {
        for_each = ingress.value.traffic_weight
        content {
          percentage      = traffic_weight.value.percentage
          latest_revision = traffic_weight.value.latest_revision
          revision_suffix = lookup(traffic_weight.value, "revision_suffix", null)
          label           = lookup(traffic_weight.value, "label", null)
        }
      }
    }
  }

  template {
    dynamic "container" {
      for_each = each.value.template.containers
      content {
        name   = container.value.name
        image  = container.value.image
        cpu    = container.value.cpu
        memory = container.value.memory

        dynamic "env" {
          for_each = container.value.env
          content {
            name        = env.value.name
            value       = lookup(env.value, "value", null)
            secret_name = lookup(env.value, "secret_name", null)
          }
        }
      }
    }

    min_replicas = each.value.template.min_replicas
    max_replicas = each.value.template.max_replicas
  }

  dynamic "registry" {
    for_each = each.value.registry != null ? [each.value.registry] : []
    content {
      server   = registry.value.server
      identity = lookup(registry.value, "identity", null) == null ? azurerm_user_assigned_identity.container_app_identity.id : registry.value.identity
    }
  }

  dynamic "dapr" {
    for_each = each.value.dapr != null ? [each.value.dapr] : []
    content {
      app_id       = dapr.value.app_id
      app_port     = lookup(dapr.value, "app_port", null)
      app_protocol = lookup(dapr.value, "app_protocol", "http")
    }
  }

  tags = each.value.tags

  lifecycle {
    ignore_changes = [
      template[0].container[0].image,
      # If you have multiple containers per app and want to ignore all their images:
      # template[0].container[*].image
    ]
  }
}

# 6 - Key Vaults for Container Apps
resource "azurerm_key_vault" "container_app_kv" {
  for_each = { # Iterate over the distinct key vault names specified in container_apps
    for app in var.container_apps :
    app.key_vault_name => app.key_vault_name
    if app.key_vault_name != null
  }
  name                = each.value
  location            = var.location
  resource_group_name = var.resource_group_name
  sku_name            = var.key_vault_sku
  tenant_id           = data.azurerm_client_config.current.tenant_id

  # Modern soft delete and purge protection settings
  soft_delete_retention_days = 90
  purge_protection_enabled   = true

  # Network ACLs - default to allow trusted Azure services
  network_acls {
    default_action = "Allow"
    bypass         = "AzureServices"
  }

  tags = var.tags
}

# Define Container Apps in the environment

locals {
  app_kv_policy_combinations = flatten([
    for app_config in var.container_apps : [
      {
        app_name = app_config.name
        kv_name  = app_config.key_vault_name
      }
    ]
    if app_config.key_vault_name != null
  ])
}

# 7 - Grant Key Vault access to Container App's System Assigned Identity
resource "azurerm_key_vault_access_policy" "container_app_system_identity_access" {
  for_each = { # Ensure unique keys for for_each by combining app and kv names
    for item in local.app_kv_policy_combinations :
    "${item.app_name}-${item.kv_name}" => item
    if item.kv_name != null # Only create policies if a key_vault_name is specified
  }

  key_vault_id = azurerm_key_vault.container_app_kv[each.value.kv_name].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_container_app.apps[each.value.app_name].identity[0].principal_id

  secret_permissions = ["Get", "List"] # Hardcoded permissions

  depends_on = [
    azurerm_key_vault.container_app_kv,
    azurerm_container_app.apps,
    azurerm_private_dns_zone_virtual_network_link.container_app_env_dns_link,
  ]
}
