#!/bin/bash

# Script to set up a pre-commit git hook for Terraform format checking
# This script creates a git hook that runs 'terraform fmt -check -recursive' before commits

set -e  # Exit on any error

echo "🔧 Setting up Terraform format pre-commit git hook..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Error: Not in a git repository. Please run this script from the root of a git repository."
    exit 1
fi

# Check if terraform is available
if ! command -v terraform &> /dev/null; then
    echo "⚠️  Warning: terraform command not found. The hook will check for terraform when it runs."
fi

# Create the hooks directory if it doesn't exist
mkdir -p .git/hooks

# Check if pre-commit hook already exists
if [ -f ".git/hooks/pre-commit" ]; then
    echo "⚠️  A pre-commit hook already exists."
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Aborted. Existing pre-commit hook was not modified."
        exit 1
    fi
    echo "📝 Backing up existing pre-commit hook to pre-commit.backup..."
    cp .git/hooks/pre-commit .git/hooks/pre-commit.backup
fi

# Create the pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

# Pre-commit hook to run terraform fmt -check -recursive
# This hook prevents commits if Terraform files are not properly formatted

echo "Running Terraform format check..."

# Check if terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "Error: terraform command not found. Please install Terraform."
    exit 1
fi

# Run terraform fmt -check -recursive from the repository root
if ! terraform fmt -check -recursive; then
    echo ""
    echo "❌ Terraform format check failed!"
    echo "Please run 'terraform fmt -recursive' to fix formatting issues and try again."
    echo ""
    exit 1
fi

echo "✅ Terraform format check passed!"
exit 0
EOF

# Make the hook executable
chmod +x .git/hooks/pre-commit

echo "✅ Terraform format pre-commit hook installed successfully!"
echo ""
echo "📋 What happens now:"
echo "   • Every time you run 'git commit', terraform fmt -check will run automatically"
echo "   • If formatting issues are found, the commit will be blocked"
echo "   • Run 'terraform fmt -recursive' to fix any formatting issues"
echo ""
echo "🧪 Testing the hook..."
if .git/hooks/pre-commit; then
    echo "✅ Hook test passed! Your repository is ready."
else
    echo "❌ Hook test failed. You may need to run 'terraform fmt -recursive' first."
fi
